// مولد PDF محسن للصور الممسوحة
class PDFGenerator {
    constructor() {
        this.jsPDF = null;
        this.loadJsPDF();
    }

    // تحميل مكتبة jsPDF
    async loadJsPDF() {
        try {
            // محاولة تحميل jsPDF من node_modules
            if (typeof require !== 'undefined') {
                const { jsPDF } = require('jspdf');
                this.jsPDF = jsPDF;
            } else {
                // للمتصفح، يمكن تحميل المكتبة من CDN
                await this.loadJsPDFFromCDN();
            }
        } catch (error) {
            console.error('خطأ في تحميل jsPDF:', error);
            // استخدام طريقة بديلة
            this.jsPDF = null;
        }
    }

    // تحميل jsPDF من CDN
    async loadJsPDFFromCDN() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                this.jsPDF = window.jspdf.jsPDF;
                resolve();
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // إنشاء PDF من صورة واحدة
    async createSinglePagePDF(imageData, fileName, options = {}) {
        try {
            if (!this.jsPDF) {
                throw new Error('مكتبة PDF غير متاحة');
            }

            const pdf = new this.jsPDF({
                orientation: options.orientation || 'portrait',
                unit: 'mm',
                format: options.format || 'a4'
            });

            // إضافة معلومات الوثيقة
            pdf.setProperties({
                title: fileName,
                subject: 'مستند ممسوح ضوئياً',
                author: 'برنامج الأرشفة الإلكترونية - علي عاجل خشان المحنة',
                creator: 'برنامج الأرشفة الإلكترونية',
                producer: 'jsPDF'
            });

            // إضافة الصورة
            await this.addImageToPDF(pdf, imageData, options);

            // إضافة تاريخ المسح
            this.addScanTimestamp(pdf, options);

            return pdf;

        } catch (error) {
            console.error('خطأ في إنشاء PDF:', error);
            throw error;
        }
    }

    // إنشاء PDF متعدد الصفحات
    async createMultiPagePDF(imagesData, fileName, options = {}) {
        try {
            if (!this.jsPDF) {
                throw new Error('مكتبة PDF غير متاحة');
            }

            const pdf = new this.jsPDF({
                orientation: options.orientation || 'portrait',
                unit: 'mm',
                format: options.format || 'a4'
            });

            // إضافة معلومات الوثيقة
            pdf.setProperties({
                title: fileName,
                subject: `مستند متعدد الصفحات (${imagesData.length} صفحة)`,
                author: 'برنامج الأرشفة الإلكترونية - علي عاجل خشان المحنة',
                creator: 'برنامج الأرشفة الإلكترونية',
                producer: 'jsPDF'
            });

            // إضافة كل صفحة
            for (let i = 0; i < imagesData.length; i++) {
                if (i > 0) {
                    pdf.addPage();
                }

                await this.addImageToPDF(pdf, imagesData[i], {
                    ...options,
                    pageNumber: i + 1,
                    totalPages: imagesData.length
                });

                // إضافة رقم الصفحة
                this.addPageNumber(pdf, i + 1, imagesData.length);
            }

            // إضافة تاريخ المسح في الصفحة الأولى
            pdf.setPage(1);
            this.addScanTimestamp(pdf, options);

            return pdf;

        } catch (error) {
            console.error('خطأ في إنشاء PDF متعدد الصفحات:', error);
            throw error;
        }
    }

    // إضافة صورة إلى PDF
    async addImageToPDF(pdf, imageData, options = {}) {
        try {
            // الحصول على أبعاد الصفحة
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();
            
            // هوامش الصفحة
            const margin = options.margin || 10;
            const availableWidth = pageWidth - (margin * 2);
            const availableHeight = pageHeight - (margin * 2) - 20; // مساحة للتاريخ ورقم الصفحة

            // إضافة الصورة
            const imageFormat = this.detectImageFormat(imageData);
            
            pdf.addImage(
                imageData.data || imageData.imageData || imageData,
                imageFormat,
                margin,
                margin,
                availableWidth,
                availableHeight,
                undefined,
                'FAST'
            );

        } catch (error) {
            console.error('خطأ في إضافة الصورة:', error);
            throw error;
        }
    }

    // تحديد تنسيق الصورة
    detectImageFormat(imageData) {
        const data = imageData.data || imageData.imageData || imageData;
        
        if (data.startsWith('data:image/png')) return 'PNG';
        if (data.startsWith('data:image/jpeg') || data.startsWith('data:image/jpg')) return 'JPEG';
        if (data.startsWith('data:image/gif')) return 'GIF';
        
        // افتراضي
        return 'PNG';
    }

    // إضافة تاريخ المسح
    addScanTimestamp(pdf, options = {}) {
        const pageHeight = pdf.internal.pageSize.getHeight();
        const pageWidth = pdf.internal.pageSize.getWidth();
        
        pdf.setFontSize(8);
        pdf.setTextColor(128, 128, 128);
        
        const timestamp = new Date().toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const text = `تاريخ المسح: ${timestamp}`;
        pdf.text(text, 10, pageHeight - 5);
    }

    // إضافة رقم الصفحة
    addPageNumber(pdf, currentPage, totalPages) {
        const pageHeight = pdf.internal.pageSize.getHeight();
        const pageWidth = pdf.internal.pageSize.getWidth();
        
        pdf.setFontSize(8);
        pdf.setTextColor(128, 128, 128);
        
        const text = `صفحة ${currentPage} من ${totalPages}`;
        const textWidth = pdf.getTextWidth(text);
        pdf.text(text, pageWidth - textWidth - 10, pageHeight - 5);
    }

    // حفظ PDF
    async savePDF(pdf, fileName, outputPath) {
        try {
            if (typeof require !== 'undefined') {
                // في بيئة Electron، حفظ الملف مباشرة
                const fs = require('fs');
                const path = require('path');
                
                const pdfBuffer = pdf.output('arraybuffer');
                const fullPath = path.join(outputPath, fileName + '.pdf');
                
                fs.writeFileSync(fullPath, Buffer.from(pdfBuffer));
                return fullPath;
            } else {
                // في المتصفح، تحميل الملف
                pdf.save(fileName + '.pdf');
                return fileName + '.pdf';
            }
        } catch (error) {
            console.error('خطأ في حفظ PDF:', error);
            throw error;
        }
    }

    // طريقة بديلة لإنشاء PDF بدون jsPDF
    async createSimplePDF(imageData, fileName, outputPath) {
        try {
            // استخدام Canvas API لإنشاء PDF بسيط
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // تحميل الصورة
            const img = new Image();
            img.src = imageData.data || imageData.imageData || imageData;
            
            return new Promise((resolve, reject) => {
                img.onload = () => {
                    // تعيين أبعاد Canvas
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // رسم الصورة
                    ctx.drawImage(img, 0, 0);
                    
                    // تحويل إلى PDF (طريقة مبسطة)
                    canvas.toBlob((blob) => {
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = fileName + '.png'; // حفظ كصورة إذا فشل PDF
                        link.click();
                        resolve(fileName + '.png');
                    });
                };
                
                img.onerror = reject;
            });
            
        } catch (error) {
            console.error('خطأ في إنشاء PDF البديل:', error);
            throw error;
        }
    }
}

// إنشاء مثيل واحد من مولد PDF
const pdfGenerator = new PDFGenerator();

// تصدير للاستخدام العام
window.pdfGenerator = pdfGenerator;

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PDFGenerator;
}
