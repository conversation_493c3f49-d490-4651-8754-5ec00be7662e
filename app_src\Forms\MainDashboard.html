<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة المعلومات - برنامج الأرشفة الإلكترونية</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 50%, #CCE7FF 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: headerShine 3s ease-in-out infinite;
        }

        @keyframes headerShine {
            0% { transform: translateX(-100%) rotate(45deg); }
            100% { transform: translateX(100%) rotate(45deg); }
        }

        .header-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .quick-search {
            background: white;
            padding: 15px;
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #0066CC;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #00AAFF;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
        }

        .search-btn {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        .dashboard-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .dashboard-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 2px solid #0066CC;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            border-color: #00AAFF;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 170, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .dashboard-card:hover::before {
            left: 100%;
        }

        .card-icon {
            font-size: 64px;
            margin-bottom: 20px;
            transition: all 0.4s ease;
            position: relative;
            z-index: 1;
        }

        .dashboard-card:hover .card-icon {
            transform: scale(1.2) rotate(10deg);
        }

        .card-title {
            font-size: 22px;
            font-weight: bold;
            color: #0066CC;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .card-description {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            position: relative;
            z-index: 1;
        }

        /* ألوان مختلفة للبطاقات */
        .card-add { border-color: #00CC66; }
        .card-add .card-title { color: #00CC66; }
        .card-add .card-icon { color: #00CC66; }

        .card-search { border-color: #FF9900; }
        .card-search .card-title { color: #FF9900; }
        .card-search .card-icon { color: #FF9900; }

        .card-reports { border-color: #8A2BE2; }
        .card-reports .card-title { color: #8A2BE2; }
        .card-reports .card-icon { color: #8A2BE2; }

        .card-settings { border-color: #666666; }
        .card-settings .card-title { color: #666666; }
        .card-settings .card-icon { color: #666666; }

        .card-archive { border-color: #FF3333; }
        .card-archive .card-title { color: #FF3333; }
        .card-archive .card-icon { color: #FF3333; }

        .card-print-report { border-color: #17A2B8; } /* Teal/Info */
        .card-print-report .card-title { color: #17A2B8; }
        .card-print-report .card-icon { color: #17A2B8; }

        .card-user-mgmt { border-color: #6F42C1; } /* Indigo */
        .card-user-mgmt .card-title { color: #6F42C1; }
        .card-user-mgmt .card-icon { color: #6F42C1; }

        .card-exit { border-color: #333333; }
        .card-exit .card-title { color: #333333; }
        .card-exit .card-icon { color: #333333; }

        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-title {
            font-size: 24px;
            font-weight: bold;
            color: #0066CC;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #0066CC;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            color: #666;
        }

        #lastUpdate { /* Style for the 'last update' statistic number */
            font-size: 24px; /* Matches stats-title font-size */
        }

        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 50px;
        }

        .help-button {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: linear-gradient(45deg, #00AAFF, #0066CC);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .help-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .theme-selector {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .theme-button {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            margin: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-button:hover {
            transform: scale(1.2);
        }

        .theme-blue { background: #0066CC; }
        .theme-green { background: #00AA44; }
        .theme-purple { background: #8A2BE2; }
        .theme-sky { background: #00BFFF; }
        .theme-dark { background: #333333; }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header-title {
                font-size: 24px;
            }
            
            .dashboard-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body class="theme-blue">
    <div class="theme-selector">
        <button class="theme-button theme-blue" onclick="changeTheme('blue')" title="الثيم الأزرق"></button>
        <button class="theme-button theme-green" onclick="changeTheme('green')" title="الثيم الأخضر"></button>
        <button class="theme-button theme-purple" onclick="changeTheme('purple')" title="الثيم البنفسجي"></button>
        <button class="theme-button theme-sky" onclick="changeTheme('sky')" title="الثيم السماوي"></button>
        <button class="theme-button theme-dark" onclick="changeTheme('dark')" title="الثيم الداكن"></button>
    </div>

    <header class="header">
        <h1 class="header-title"><i class="fas fa-tachometer-alt"></i> لوحة المعلومات الرئيسية</h1>
        <p class="header-subtitle">برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</p>
    </header>



    <div class="dashboard-container">
        <div class="dashboard-grid">
            <div class="dashboard-card card-add" onclick="openPage('BookEntry.html')">
                <div class="card-icon"><i class="fas fa-plus-circle fa-2x"></i></div>
                <h3 class="card-title">إدخال بيانات كتاب جديد</h3>
                <p class="card-description">إضافة كتاب جديد إلى الأرشيف مع جميع التفاصيل والصور المطلوبة</p>
            </div>



            <div class="dashboard-card card-reports" onclick="openPage('Reports.html')">
                <div class="card-icon"><i class="fas fa-chart-line fa-2x"></i></div>
                <h3 class="card-title">التقارير والإحصائيات</h3>
                <p class="card-description">عرض التقارير التفاعلية والإحصائيات المفصلة</p>
            </div>

            <div class="dashboard-card card-settings" onclick="openPage('Settings.html')">
                <div class="card-icon"><i class="fas fa-cog fa-2x"></i></div>
                <h3 class="card-title">الإعدادات</h3>
                <p class="card-description">تخصيص إعدادات النظام والثيمات والنسخ الاحتياطي</p>
            </div>

            <div class="dashboard-card card-archive" onclick="openAdminPanel()">
                <div class="card-icon"><i class="fas fa-archive fa-2x"></i></div>
                <h3 class="card-title">الأرشيف العام</h3>
                <p class="card-description">إدارة الأرشيف الكامل (صلاحية الأدمن فقط)</p>
            </div>

            <div class="dashboard-card card-print-report" onclick="alert('Print Report functionality to be implemented.');">
                <div class="card-icon"><i class="fas fa-print fa-2x"></i></div>
                <h3 class="card-title">طباعة تقرير</h3>
                <p class="card-description">إنشاء وطباعة التقارير المخصصة</p>
            </div>

            <div class="dashboard-card card-user-mgmt" onclick="alert('User Management section to be implemented.');">
                <div class="card-icon"><i class="fas fa-users-cog fa-2x"></i></div>
                <h3 class="card-title">إدارة المستخدمين</h3>
                <p class="card-description">إدارة حسابات المستخدمين وصلاحياتهم</p>
            </div>

            <div class="dashboard-card card-exit" onclick="exitApplication()">
                <div class="card-icon"><i class="fas fa-sign-out-alt fa-2x"></i></div>
                <h3 class="card-title">خروج من التطبيق</h3>
                <p class="card-description">إغلاق البرنامج بأمان</p>
            </div>
        </div>

        <div class="stats-section">
            <h2 class="stats-title"><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalBooks">0</div>
                    <div class="stat-label">إجمالي الكتب</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="monthlyBooks">0</div>
                    <div class="stat-label">كتب هذا الشهر</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalImages">0</div>
                    <div class="stat-label">إجمالي الصور</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="lastUpdate">اليوم</div>
                    <div class="stat-label">آخر تحديث</div>
                </div>
            </div>
        </div>
    </div>

    <button class="help-button" onclick="showHelp()" title="المساعدة"><i class="fas fa-question-circle"></i></button>

    <footer class="footer">
        <p>© 2024 برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</p>
        <p>قسم الحسابات - شعبة الرواتب</p>
    </footer>

    <script src="../Database/books_database.js"></script>
    <script>
        // تحميل الإحصائيات من قاعدة البيانات
        async function loadStatistics() {
            try {
                const stats = await booksDB.getStatistics();

                document.getElementById('totalBooks').textContent = stats.totalBooks.toString();
                document.getElementById('monthlyBooks').textContent = stats.monthlyBooks.toString();
                document.getElementById('totalImages').textContent = stats.totalImages.toString();
                document.getElementById('lastUpdate').textContent = stats.lastUpdate;
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
                document.getElementById('totalBooks').textContent = '0';
                document.getElementById('monthlyBooks').textContent = '0';
                document.getElementById('totalImages').textContent = '0';
                document.getElementById('lastUpdate').textContent = '0';
            }
        }

        // تغيير الثيم
        function changeTheme(theme) {
            document.body.className = 'theme-' + theme;
            localStorage.setItem('selectedTheme', theme);
        }

        // فتح صفحة
        function openPage(page) {
            window.location.href = page;
        }

        // تم حذف وظيفة البحث السريع

        // فتح لوحة الأدمن
        function openAdminPanel() {
            showPasswordModal();
        }

        // إظهار نافذة كلمة المرور
        function showPasswordModal() {
            const modalHTML = `
                <div id="passwordModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.7);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        text-align: center;
                        min-width: 400px;
                    ">
                        <h2 style="color: #FF3333; margin-bottom: 20px;">🔒 دخول منطقة الأدمن</h2>
                        <p style="margin-bottom: 20px; color: #666;">أدخل كلمة مرور الأدمن للوصول إلى الأدوات المتقدمة</p>
                        <input type="password" id="adminPassword" placeholder="كلمة المرور" style="
                            width: 100%;
                            padding: 15px;
                            border: 2px solid #FF3333;
                            border-radius: 8px;
                            font-size: 16px;
                            margin-bottom: 20px;
                            box-sizing: border-box;
                            text-align: center;
                            font-family: monospace;
                        ">
                        <div>
                            <button onclick="checkAdminPassword()" style="
                                background: linear-gradient(45deg, #FF3333, #FF6666);
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-size: 16px;
                                font-weight: bold;
                                margin: 5px;
                            ">✅ دخول</button>
                            <button onclick="closePasswordModal()" style="
                                background: #666;
                                color: white;
                                border: none;
                                padding: 12px 25px;
                                border-radius: 8px;
                                cursor: pointer;
                                font-size: 16px;
                                font-weight: bold;
                                margin: 5px;
                            ">❌ إلغاء</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            document.getElementById('adminPassword').focus();

            // إضافة مستمع للضغط على Enter
            document.getElementById('adminPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkAdminPassword();
                }
            });
        }

        // فحص كلمة مرور الأدمن
        function checkAdminPassword() {
            const password = document.getElementById('adminPassword').value;
            if (password === 'a1234a1234A@#1') {
                closePasswordModal();
                window.location.href = 'AdminPanel.html';
            } else if (password.trim() !== '') {
                alert('❌ كلمة المرور غير صحيحة!\n\nيرجى المحاولة مرة أخرى.');
                document.getElementById('adminPassword').value = '';
                document.getElementById('adminPassword').focus();
            }
        }

        // إغلاق نافذة كلمة المرور
        function closePasswordModal() {
            const modal = document.getElementById('passwordModal');
            if (modal) {
                modal.remove();
            }
        }

        // إظهار المساعدة
        function showHelp() {
            alert(`مرحباً بك في برنامج الأرشفة الإلكترونية للكتب

الوظائف الرئيسية:
• إدخال بيانات كتاب جديد: لإضافة كتب جديدة للأرشيف
• البحث والاستعراض: للبحث في الكتب الموجودة
• التقارير: لعرض التقارير والإحصائيات
• الإعدادات: لتخصيص النظام
• الأرشيف العام: للإدارة الكاملة (أدمن فقط)

للمساعدة الإضافية، راجع دليل المستخدم.`);
        }

        // الخروج من التطبيق
        function exitApplication() {
            if (confirm('هل تريد الخروج من التطبيق؟')) {
                window.close();
            }
        }

        // تحميل الثيم المحفوظ
        window.addEventListener('load', () => {
            const savedTheme = localStorage.getItem('selectedTheme') || 'blue';
            changeTheme(savedTheme);
            loadStatistics();
        });

        // البحث بالضغط على Enter - quickSearchInput element is not in the HTML body
        // document.getElementById('quickSearchInput').addEventListener('keypress', (e) => {
        //     if (e.key === 'Enter') {
        //         quickSearch(); // quickSearch function is commented out
        //     }
        // });
    </script>
</body>
</html>
