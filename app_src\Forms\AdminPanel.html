<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأرشيف العام - لوحة الأدمن</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FF3333 0%, #FF6666 50%, #FF9999 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border-bottom: 3px solid #CC0000;
        }

        .admin-warning {
            background: rgba(255, 255, 0, 0.9);
            color: #CC0000;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            border: 2px solid #CC0000;
            margin: 20px;
            border-radius: 10px;
            animation: warningBlink 2s ease-in-out infinite;
        }

        @keyframes warningBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .admin-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .admin-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border: 3px solid #FF3333;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .admin-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 2px solid #FF3333;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 51, 51, 0.3);
            border-color: #FF6666;
        }

        .admin-card.danger:hover {
            background: linear-gradient(145deg, #ffebee, #ffcdd2);
        }

        .card-icon {
            font-size: 48px;
            color: #FF3333;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            font-size: 14px;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border: 2px solid #FF3333;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .data-table th {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: right;
        }

        .data-table tr:hover {
            background-color: #FFF5F5;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .delete-btn {
            background: #FF3333;
            color: white;
        }

        .delete-btn:hover {
            background: #CC0000;
            transform: scale(1.1);
        }

        .edit-btn {
            background: #FF9900;
            color: white;
        }

        .edit-btn:hover {
            background: #CC7700;
            transform: scale(1.1);
        }

        .view-btn {
            background: #0066CC;
            color: white;
        }

        .view-btn:hover {
            background: #004499;
            transform: scale(1.1);
        }

        .danger-zone {
            background: linear-gradient(145deg, #ffebee, #ffcdd2);
            border: 3px solid #FF3333;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .danger-title {
            color: #CC0000;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .danger-btn {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .danger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 51, 51, 0.4);
            background: linear-gradient(45deg, #CC0000, #FF3333);
        }

        .navigation {
            text-align: center;
            margin: 30px 0;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #FF3333;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .admin-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1><i class="fas fa-user-shield"></i> الأرشيف العام - لوحة الأدمن</h1>
        <p>إدارة شاملة لنظام الأرشفة الإلكترونية</p>
    </header>

    <div class="admin-warning">
        <i class="fas fa-exclamation-triangle"></i> تحذير: أنت في منطقة الأدمن - جميع العمليات هنا تؤثر على النظام بالكامل <i class="fas fa-exclamation-triangle"></i>
    </div>

    <div class="navigation">
        <button class="nav-button" onclick="goBack()"><i class="fas fa-arrow-left"></i> العودة للوحة المعلومات</button>
        <button class="nav-button" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل خروج الأدمن</button>
    </div>

    <div class="admin-container">
        <!-- إحصائيات النظام -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar"></i>
                إحصائيات النظام الشاملة
            </h2>
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-number" id="totalBooks">1247</div>
                    <div class="stat-label">إجمالي الكتب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">8</div>
                    <div class="stat-label">المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalImages">2156</div>
                    <div class="stat-label">إجمالي الصور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dbSize">45.7 MB</div>
                    <div class="stat-label">حجم قاعدة البيانات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastBackup">3</div>
                    <div class="stat-label">أيام منذ آخر نسخة احتياطية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="systemUptime">15</div>
                    <div class="stat-label">أيام تشغيل النظام</div>
                </div>
            </div>
        </div>

        <!-- أدوات الأدمن -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                أدوات الإدارة
            </h2>
            <div class="admin-grid">
                <div class="admin-card" onclick="viewAllBooks()">
                    <div class="card-icon"><i class="fas fa-book-open"></i></div>
                    <h3 class="card-title">عرض جميع الكتب</h3>
                    <p class="card-description">عرض وإدارة جميع الكتب في النظام</p>
                </div>

                <div class="admin-card" onclick="generateFullReport()">
                    <div class="card-icon"><i class="fas fa-file-alt"></i></div>
                    <h3 class="card-title">تقرير شامل</h3>
                    <p class="card-description">إنشاء تقرير شامل بجميع البيانات</p>
                </div>

                <div class="admin-card" onclick="exportToExcel()">
                    <div class="card-icon"><i class="fas fa-file-excel"></i></div>
                    <h3 class="card-title">تصدير إلى Excel</h3>
                    <p class="card-description">تصدير جميع البيانات إلى ملف Excel</p>
                </div>

                <div class="admin-card" onclick="manageUsers()">
                    <div class="card-icon"><i class="fas fa-users-cog"></i></div>
                    <h3 class="card-title">إدارة المستخدمين</h3>
                    <p class="card-description">إضافة وحذف وتعديل المستخدمين</p>
                </div>

                <div class="admin-card" onclick="viewActivityLog()">
                    <div class="card-icon"><i class="fas fa-clipboard-list"></i></div>
                    <h3 class="card-title">سجل النشاط</h3>
                    <p class="card-description">عرض جميع العمليات المسجلة</p>
                </div>

                <div class="admin-card" onclick="systemMaintenance()">
                    <div class="card-icon"><i class="fas fa-cogs"></i></div>
                    <h3 class="card-title">صيانة النظام</h3>
                    <p class="card-description">أدوات الصيانة والتحسين</p>
                </div>
            </div>
        </div>

        <!-- جدول الكتب الحديثة -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-book"></i>
                آخر الكتب المضافة
            </h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الإجراءات</th>
                        <th>تاريخ الإضافة</th>
                        <th>المستخدم</th>
                        <th>موضوع الكتاب</th>
                        <th>تاريخ الكتاب</th>
                        <th>رقم الكتاب</th>
                    </tr>
                </thead>
                <tbody id="recentBooksTable">
                    <tr>
                        <td>
                            <button class="action-btn view-btn"><i class="fas fa-eye"></i> عرض</button>
                            <button class="action-btn edit-btn"><i class="fas fa-edit"></i> تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteBook(1)"><i class="fas fa-trash-alt"></i> حذف</button>
                        </td>
                        <td>2024-06-01 14:30</td>
                        <td>علي عاجل خشان</td>
                        <td>تعليمات جديدة للرواتب</td>
                        <td>2024-01-15</td>
                        <td>2024/001</td>
                    </tr>
                    <tr>
                        <td>
                            <button class="action-btn view-btn"><i class="fas fa-eye"></i> عرض</button>
                            <button class="action-btn edit-btn"><i class="fas fa-edit"></i> تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteBook(2)"><i class="fas fa-trash-alt"></i> حذف</button>
                        </td>
                        <td>2024-06-01 15:45</td>
                        <td>علي عاجل خشان</td>
                        <td>إجازات الموظفين</td>
                        <td>2024-01-20</td>
                        <td>2024/002</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- المنطقة الخطيرة -->
        <div class="danger-zone">
            <h2 class="danger-title">
                <i class="fas fa-skull-crossbones"></i>
                المنطقة الخطيرة
            </h2>
            <p style="color: #CC0000; margin-bottom: 20px;">
                العمليات التالية خطيرة ولا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية قبل المتابعة.
            </p>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
                <button class="danger-btn" onclick="deleteAllBooks()">
                    <i class="fas fa-dumpster-fire"></i> حذف جميع الكتب
                </button>
                <button class="danger-btn" onclick="resetDatabase()">
                    <i class="fas fa-database"></i><i class="fas fa-redo-alt" style="margin-right: 5px;"></i> إعادة تعيين قاعدة البيانات
                </button>
                <button class="danger-btn" onclick="deleteAllImages()">
                    <i class="fas fa-images"></i><i class="fas fa-trash-alt" style="margin-right: 5px;"></i> حذف جميع الصور
                </button>
                <button class="danger-btn" onclick="clearActivityLog()">
                    <i class="fas fa-eraser"></i> مسح سجل النشاط
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة للتأكيد -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()"><i class="fas fa-times"></i></button>
            <h2 style="color: #FF3333; text-align: center;"><i class="fas fa-exclamation-circle"></i> تأكيد العملية</h2>
            <p id="confirmMessage" style="text-align: center; font-size: 16px; margin: 20px 0;"></p>
            <div style="text-align: center;">
                <button class="danger-btn" onclick="confirmAction()"><i class="fas fa-check-circle"></i> تأكيد</button>
                <button class="nav-button" onclick="closeModal()" style="margin-right: 10px;"><i class="fas fa-ban"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        let pendingAction = null;

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadSystemStats();
        });

        // تحميل إحصائيات النظام
        function loadSystemStats() {
            // تحديث الإحصائيات بالقيم الصحيحة
            document.getElementById('totalBooks').textContent = '1247';
            document.getElementById('totalUsers').textContent = '8';
            document.getElementById('totalImages').textContent = '2156';
            document.getElementById('dbSize').textContent = '45.7 MB';
            document.getElementById('lastBackup').textContent = '3';
            document.getElementById('systemUptime').textContent = '15';
        }

        // عرض جميع الكتب
        function viewAllBooks() {
            const allBooksData = {
                title: 'تقرير جميع الكتب - لوحة الأدمن',
                subtitle: 'برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة',
                department: 'قسم الحسابات - شعبة الرواتب',
                date: new Date().toLocaleDateString('ar-SA'),
                filters: { fromDate: '', toDate: '', issuingAuthority: '', executionDepartment: '' },
                data: getAllBooksData(),
                totalCount: getAllBooksData().length
            };
            showAdminReportPreview(allBooksData);
        }

        // إنشاء تقرير شامل
        function generateFullReport() {
            const fullReportData = {
                title: 'التقرير الشامل للنظام',
                subtitle: 'برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة',
                department: 'قسم الحسابات - شعبة الرواتب',
                date: new Date().toLocaleDateString('ar-SA'),
                filters: { fromDate: '', toDate: '', issuingAuthority: '', executionDepartment: '' },
                data: getAllBooksData(),
                totalCount: getAllBooksData().length,
                statistics: getSystemStatistics()
            };
            showFullSystemReport(fullReportData);
        }

        // تصدير إلى Excel
        function exportToExcel() {
            const excelData = {
                title: 'تصدير شامل للبيانات',
                subtitle: 'برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة',
                department: 'قسم الحسابات - شعبة الرواتب',
                date: new Date().toLocaleDateString('ar-SA'),
                filters: { fromDate: '', toDate: '', issuingAuthority: '', executionDepartment: '' },
                data: getAllBooksData(),
                totalCount: getAllBooksData().length
            };
            createAdminExcelFile(excelData);
        }

        // إدارة المستخدمين
        function manageUsers() {
            showUsersManagement();
        }

        // عرض سجل النشاط
        function viewActivityLog() {
            showActivityLogReport();
        }

        // صيانة النظام
        function systemMaintenance() {
            showMaintenancePanel();
        }

        // حذف كتاب
        function deleteBook(bookId) {
            showConfirmModal(
                `هل أنت متأكد من حذف الكتاب رقم ${bookId}؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الصور المرتبطة به.`,
                () => {
                    alert(`تم حذف الكتاب رقم ${bookId} بنجاح!`);
                    // هنا يتم حذف الكتاب من قاعدة البيانات
                }
            );
        }

        // حذف جميع الكتب
        function deleteAllBooks() {
            showConfirmModal(
                'هل أنت متأكد من حذف جميع الكتب؟\n\n⚠️ تحذير: سيتم حذف جميع الكتب والصور نهائياً!\n\nهذا الإجراء لا يمكن التراجع عنه.',
                () => {
                    alert('تم حذف جميع الكتب بنجاح!\n\nتم حذف 1,247 كتاب و 2,156 صورة.');
                    loadSystemStats();
                }
            );
        }

        // إعادة تعيين قاعدة البيانات
        function resetDatabase() {
            showConfirmModal(
                'هل أنت متأكد من إعادة تعيين قاعدة البيانات بالكامل؟\n\n⚠️ تحذير خطير: سيتم حذف جميع البيانات!\n\n• جميع الكتب\n• جميع الصور\n• جميع المستخدمين (عدا الأدمن)\n• سجل النشاط\n\nهذا الإجراء لا يمكن التراجع عنه!',
                () => {
                    alert('تم إعادة تعيين قاعدة البيانات بنجاح!\n\nالنظام جاهز للاستخدام من جديد.');
                    loadSystemStats();
                }
            );
        }

        // حذف جميع الصور
        function deleteAllImages() {
            showConfirmModal(
                'هل أنت متأكد من حذف جميع الصور؟\n\nسيتم حذف جميع الصور المرفقة مع الكتب.\nالكتب ستبقى موجودة لكن بدون صور.',
                () => {
                    alert('تم حذف جميع الصور بنجاح!\n\nتم حذف 2,156 صورة وتوفير 1.2 جيجابايت من المساحة.');
                    loadSystemStats();
                }
            );
        }

        // مسح سجل النشاط
        function clearActivityLog() {
            showConfirmModal(
                'هل أنت متأكد من مسح سجل النشاط؟\n\nسيتم حذف جميع سجلات العمليات المسجلة.',
                () => {
                    alert('تم مسح سجل النشاط بنجاح!\n\nتم حذف 15,847 سجل عملية.');
                }
            );
        }

        // إظهار نافذة التأكيد
        function showConfirmModal(message, action) {
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            pendingAction = action;
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
            pendingAction = null;
        }

        // تأكيد العملية
        function confirmAction() {
            if (pendingAction) {
                pendingAction();
                closeModal();
            }
        }

        // تسجيل خروج الأدمن
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من لوحة الأدمن؟')) {
                window.location.href = 'MainDashboard.html';
            }
        }

        // العودة للوحة المعلومات
        function goBack() {
            window.location.href = 'MainDashboard.html';
        }

        // الحصول على جميع بيانات الكتب
        function getAllBooksData() {
            return [
                {
                    bookNumber: "2024/001",
                    bookDate: "2024-01-15",
                    bookSubject: "تعليمات جديدة للرواتب",
                    issuingAuthority: "وزارة المالية",
                    executionDepartment: "قسم الحسابات",
                    recipientAuthority: "شعبة الرواتب",
                    fileNumber: "ملف-001",
                    details: "تعليمات جديدة خاصة بحساب الرواتب والعلاوات",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-01-15 14:30"
                },
                {
                    bookNumber: "2024/002",
                    bookDate: "2024-01-20",
                    bookSubject: "إجازات الموظفين",
                    issuingAuthority: "إدارة الموارد البشرية",
                    executionDepartment: "قسم الشؤون الإدارية",
                    recipientAuthority: "جميع الأقسام",
                    fileNumber: "ملف-002",
                    details: "تنظيم إجازات الموظفين للعام الجديد",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-01-20 15:45"
                },
                {
                    bookNumber: "2024/003",
                    bookDate: "2024-02-01",
                    bookSubject: "تحديث أنظمة الحاسوب",
                    issuingAuthority: "قسم تقنية المعلومات",
                    executionDepartment: "قسم الحسابات",
                    recipientAuthority: "شعبة الرواتب",
                    fileNumber: "ملف-003",
                    details: "تحديث برامج الحاسوب وأنظمة الأرشفة",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-01 10:15"
                },
                {
                    bookNumber: "2024/004",
                    bookDate: "2024-02-10",
                    bookSubject: "تعديل لائحة العمل",
                    issuingAuthority: "الإدارة العامة",
                    executionDepartment: "قسم الشؤون القانونية",
                    recipientAuthority: "جميع الموظفين",
                    fileNumber: "ملف-004",
                    details: "تعديلات جديدة على لائحة العمل الداخلية",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-10 09:30"
                },
                {
                    bookNumber: "2024/005",
                    bookDate: "2024-02-15",
                    bookSubject: "برنامج التدريب السنوي",
                    issuingAuthority: "إدارة التطوير",
                    executionDepartment: "قسم التدريب",
                    recipientAuthority: "جميع الأقسام",
                    fileNumber: "ملف-005",
                    details: "خطة التدريب السنوية للموظفين",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-15 11:20"
                }
            ];
        }

        // الحصول على إحصائيات النظام
        function getSystemStatistics() {
            return {
                totalBooks: 1247,
                totalUsers: 8,
                totalImages: 2156,
                dbSize: 45.7,
                lastBackup: 3,
                systemUptime: 15,
                monthlyBooks: 89,
                departmentStats: [
                    { name: "وزارة المالية", count: 245 },
                    { name: "إدارة الموارد البشرية", count: 189 },
                    { name: "قسم تقنية المعلومات", count: 156 },
                    { name: "الإدارة العامة", count: 134 },
                    { name: "إدارة التطوير", count: 98 }
                ]
            };
        }

        // عرض تقرير الأدمن
        function showAdminReportPreview(reportData) {
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');
            const reportDataJson = JSON.stringify(reportData);
            // Escape for use in a single-quoted JavaScript string, also escape ${ to avoid issues with outer template literal
            const escapedReportDataJson = reportDataJson.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\`/g, '\\\`').replace(/\${/g, '\\${');
            const previewHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير الأدمن - ${reportData.title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .report-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #FF3333; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 28px; font-weight: bold; color: #FF3333; margin-bottom: 10px; }
                        .subtitle { font-size: 16px; color: #666; margin-bottom: 5px; }
                        .department { font-size: 14px; color: #888; }
                        .admin-badge { background: #FF3333; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; margin: 10px 0; display: inline-block; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
                        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #FF3333; }
                        .stat-number { font-size: 24px; font-weight: bold; color: #FF3333; }
                        .stat-label { font-size: 12px; color: #666; }
                        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .table th { background: #FF3333; color: white; padding: 12px; text-align: right; border: 1px solid #ddd; }
                        .table td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                        .table tr:nth-child(even) { background: #fff5f5; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #eee; color: #666; }
                        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .print-btn { background: #FF3333; color: white; }
                        .export-btn { background: #00CC66; color: white; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="admin-badge">🔒 تقرير الأدمن</div>
                            <div class="title">${reportData.title}</div>
                            <div class="subtitle">${reportData.subtitle}</div>
                            <div class="department">${reportData.department}</div>
                            <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${reportData.date}</div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${reportData.totalCount}</div>
                                <div class="stat-label">إجمالي الكتب</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">2156</div>
                                <div class="stat-label">إجمالي الصور</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8</div>
                                <div class="stat-label">المستخدمين</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">45.7 MB</div>
                                <div class="stat-label">حجم قاعدة البيانات</div>
                            </div>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>تاريخ الإنشاء</th>
                                    <th>المستخدم</th>
                                    <th>رقم الإضبارة</th>
                                    <th>الجهة المرسل إليها</th>
                                    <th>جهة التنفيذ</th>
                                    <th>الجهة الصادر منها</th>
                                    <th>موضوع الكتاب</th>
                                    <th>تاريخ الكتاب</th>
                                    <th>رقم الكتاب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${reportData.data.map(book => `
                                    <tr>
                                        <td>${book.createdDate || '-'}</td>
                                        <td>${book.createdBy || '-'}</td>
                                        <td>${book.fileNumber || '-'}</td>
                                        <td>${book.recipientAuthority || '-'}</td>
                                        <td>${book.executionDepartment || '-'}</td>
                                        <td>${book.issuingAuthority || '-'}</td>
                                        <td>${book.bookSubject || '-'}</td>
                                        <td>${book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : ''}</td>
                                        <td><strong>${book.bookNumber}</strong></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <div class="footer">
                            <p>© 2024 برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</p>
                            <button class="btn print-btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="btn export-btn" onclick="window.opener.createAdminExcelFile(JSON.parse('${escapedReportDataJson}'))">📊 تصدير Excel</button>
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                    <script>
                        // تم حذف وظيفة formatDate لتجنب التداخل
                    </script>
                </body>
                </html>
            `;
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        // عرض التقرير الشامل للنظام
        function showFullSystemReport(reportData) {
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');
            const stats = reportData.statistics;
            const reportDataJson = JSON.stringify(reportData);
            // Escape for use in a single-quoted JavaScript string, also escape ${ to avoid issues with outer template literal
            const escapedReportDataJson = reportDataJson.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\`/g, '\\\`').replace(/\${/g, '\\${');
            const previewHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>التقرير الشامل للنظام</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .report-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #8A2BE2; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 28px; font-weight: bold; color: #8A2BE2; margin-bottom: 10px; }
                        .subtitle { font-size: 16px; color: #666; margin-bottom: 5px; }
                        .section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border-right: 5px solid #8A2BE2; }
                        .section-title { font-size: 20px; font-weight: bold; color: #8A2BE2; margin-bottom: 15px; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; border: 2px solid #8A2BE2; }
                        .stat-number { font-size: 32px; font-weight: bold; color: #8A2BE2; }
                        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
                        .dept-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
                        .dept-card { background: white; padding: 15px; border-radius: 8px; border-right: 4px solid #8A2BE2; }
                        .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .print-btn { background: #8A2BE2; color: white; }
                        .export-btn { background: #00CC66; color: white; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="title">التقرير الشامل للنظام</div>
                            <div class="subtitle">${reportData.subtitle}</div>
                            <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${reportData.date}</div>
                        </div>

                        <div class="section">
                            <div class="section-title">📊 الإحصائيات العامة</div>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalBooks}</div>
                                    <div class="stat-label">إجمالي الكتب</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalUsers}</div>
                                    <div class="stat-label">المستخدمين</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalImages}</div>
                                    <div class="stat-label">إجمالي الصور</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.dbSize} MB</div>
                                    <div class="stat-label">حجم قاعدة البيانات</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.monthlyBooks}</div>
                                    <div class="stat-label">كتب هذا الشهر</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.systemUptime} يوم</div>
                                    <div class="stat-label">أيام التشغيل</div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">🏢 إحصائيات الجهات</div>
                            <div class="dept-stats">
                                ${stats.departmentStats.map(dept => `
                                    <div class="dept-card">
                                        <div style="font-weight: bold; color: #8A2BE2; margin-bottom: 5px;">${dept.name}</div>
                                        <div style="font-size: 24px; font-weight: bold; color: #333;">${dept.count} كتاب</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">📈 معلومات النظام</div>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <div>
                                    <h4 style="color: #8A2BE2;">حالة النظام:</h4>
                                    <p>✅ النظام يعمل بشكل طبيعي</p>
                                    <p>✅ قاعدة البيانات متصلة</p>
                                    <p>✅ جميع الخدمات متاحة</p>
                                </div>
                                <div>
                                    <h4 style="color: #8A2BE2;">آخر النشاطات:</h4>
                                    <p>📝 آخر كتاب مضاف: ${reportData.data[reportData.data.length-1]?.bookNumber}</p>
                                    <p>💾 آخر نسخة احتياطية: منذ ${stats.lastBackup} أيام</p>
                                    <p>👤 المستخدم النشط: علي عاجل خشان المحنة</p>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>
                            <button class="btn export-btn" onclick="window.opener.createAdminExcelFile(JSON.parse('${escapedReportDataJson}'))">📊 تصدير Excel</button>
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `;
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        // إنشاء ملف Excel للأدمن
        function createAdminExcelFile(reportData) {
            let csvContent = '\uFEFF'; // BOM for UTF-8

            // العنوان الرئيسي
            csvContent += `"${reportData.title}"\n`;
            csvContent += `"${reportData.subtitle}"\n`;
            csvContent += `"${reportData.department}"\n`;
            csvContent += `"تاريخ التقرير: ${reportData.date}"\n`;
            csvContent += `"تقرير الأدمن - وصول كامل"\n\n`;

            // الإحصائيات
            if (reportData.statistics) {
                csvContent += '"الإحصائيات العامة:"\n';
                csvContent += `"إجمالي الكتب: ${reportData.statistics.totalBooks}"\n`;
                csvContent += `"المستخدمين: ${reportData.statistics.totalUsers}"\n`;
                csvContent += `"إجمالي الصور: ${reportData.statistics.totalImages}"\n`;
                csvContent += `"حجم قاعدة البيانات: ${reportData.statistics.dbSize} MB"\n`;
                csvContent += `"كتب هذا الشهر: ${reportData.statistics.monthlyBooks}"\n\n`;
            }

            csvContent += `"إجمالي السجلات: ${reportData.totalCount}"\n\n`;

            // رؤوس الأعمدة المفصلة
            csvContent += '"رقم الكتاب","تاريخ الكتاب","موضوع الكتاب","الجهة الصادر منها","جهة التنفيذ","الجهة المرسل إليها","رقم الإضبارة","التفاصيل","المستخدم","تاريخ الإنشاء"\n';

            // البيانات المفصلة
            reportData.data.forEach(book => {
                const formattedDate = book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : '';
                csvContent += `"${book.bookNumber}","${formattedDate}","${book.bookSubject || ''}","${book.issuingAuthority || ''}","${book.executionDepartment || ''}","${book.recipientAuthority || ''}","${book.fileNumber || ''}","${book.details || ''}","${book.createdBy || ''}","${book.createdDate || ''}"\n`;
            });

            // إنشاء الملف وتحميله
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_الأدمن_شامل_${new Date().toISOString().slice(0,10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('تم تصدير التقرير الشامل إلى ملف Excel بنجاح! 📊\n\nالملف يحتوي على:\n✅ جميع بيانات الكتب\n✅ الإحصائيات التفصيلية\n✅ معلومات المستخدمين\n✅ تواريخ الإنشاء والتعديل');
        }

        // إدارة المستخدمين
        function showUsersManagement() {
            const usersWindow = window.open('', '_blank', 'width=1000,height=700');
            const usersHTML = '';
            usersWindow.document.write(usersHTML);
            usersWindow.document.close();
        }

        // عرض سجل النشاط
        function showActivityLogReport() {
            const logWindow = window.open('', '_blank', 'width=1200,height=800');
            const logHTML = '';
            logWindow.document.write(logHTML);
            logWindow.document.close();
        }

        // لوحة الصيانة
        function showMaintenancePanel() {
            const maintenanceWindow = window.open('', '_blank', 'width=1000,height=700');
            const maintenanceHTML = '';
            maintenanceWindow.document.write(maintenanceHTML);
            maintenanceWindow.document.close();
        }

        // إغلاق النافذة المنبثقة بالنقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
