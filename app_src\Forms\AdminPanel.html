<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأرشيف العام - لوحة الأدمن</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FF3333 0%, #FF6666 50%, #FF9999 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border-bottom: 3px solid #CC0000;
        }

        .admin-warning {
            background: rgba(255, 255, 0, 0.9);
            color: #CC0000;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            border: 2px solid #CC0000;
            margin: 20px;
            border-radius: 10px;
            animation: warningBlink 2s ease-in-out infinite;
        }

        @keyframes warningBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .admin-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .admin-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border: 3px solid #FF3333;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .admin-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 2px solid #FF3333;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 51, 51, 0.3);
            border-color: #FF6666;
        }

        .admin-card.danger:hover {
            background: linear-gradient(145deg, #ffebee, #ffcdd2);
        }

        .card-icon {
            font-size: 48px;
            color: #FF3333;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            font-size: 14px;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border: 2px solid #FF3333;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .data-table th {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: right;
        }

        .data-table tr:hover {
            background-color: #FFF5F5;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .delete-btn {
            background: #FF3333;
            color: white;
        }

        .delete-btn:hover {
            background: #CC0000;
            transform: scale(1.1);
        }

        .edit-btn {
            background: #FF9900;
            color: white;
        }

        .edit-btn:hover {
            background: #CC7700;
            transform: scale(1.1);
        }

        .view-btn {
            background: #0066CC;
            color: white;
        }

        .view-btn:hover {
            background: #004499;
            transform: scale(1.1);
        }

        .danger-zone {
            background: linear-gradient(145deg, #ffebee, #ffcdd2);
            border: 3px solid #FF3333;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .danger-title {
            color: #CC0000;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .danger-btn {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .danger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 51, 51, 0.4);
            background: linear-gradient(45deg, #CC0000, #FF3333);
        }

        .navigation {
            text-align: center;
            margin: 30px 0;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #FF3333;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .admin-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1><i class="fas fa-user-shield"></i> الأرشيف العام - لوحة الأدمن</h1>
        <p>إدارة شاملة لنظام الأرشفة الإلكترونية</p>
    </header>

    <div class="admin-warning">
        <i class="fas fa-exclamation-triangle"></i> تحذير: أنت في منطقة الأدمن - جميع العمليات هنا تؤثر على النظام بالكامل <i class="fas fa-exclamation-triangle"></i>
    </div>

    <div class="navigation">
        <button class="nav-button" onclick="goBack()"><i class="fas fa-arrow-left"></i> العودة للوحة المعلومات</button>
        <button class="nav-button" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل خروج الأدمن</button>
    </div>

    <div class="admin-container">
        <!-- إحصائيات النظام -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar"></i>
                إحصائيات النظام الشاملة
            </h2>
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-number" id="totalBooks">1247</div>
                    <div class="stat-label">إجمالي الكتب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">8</div>
                    <div class="stat-label">المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalImages">2156</div>
                    <div class="stat-label">إجمالي الصور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dbSize">45.7 MB</div>
                    <div class="stat-label">حجم قاعدة البيانات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastBackup">3</div>
                    <div class="stat-label">أيام منذ آخر نسخة احتياطية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="systemUptime">15</div>
                    <div class="stat-label">أيام تشغيل النظام</div>
                </div>
            </div>
        </div>

        <!-- أدوات الأدمن -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                أدوات الإدارة
            </h2>
            <div class="admin-grid">
                <div class="admin-card" onclick="viewAllBooks()">
                    <div class="card-icon"><i class="fas fa-book-open"></i></div>
                    <h3 class="card-title">عرض جميع الكتب</h3>
                    <p class="card-description">عرض وإدارة جميع الكتب في النظام</p>
                </div>

                <div class="admin-card" onclick="generateFullReport()">
                    <div class="card-icon"><i class="fas fa-file-alt"></i></div>
                    <h3 class="card-title">تقرير شامل</h3>
                    <p class="card-description">إنشاء تقرير شامل بجميع البيانات</p>
                </div>

                <div class="admin-card" onclick="exportToExcel()">
                    <div class="card-icon"><i class="fas fa-file-excel"></i></div>
                    <h3 class="card-title">تصدير إلى Excel</h3>
                    <p class="card-description">تصدير جميع البيانات إلى ملف Excel</p>
                </div>

                <div class="admin-card" onclick="manageUsers()">
                    <div class="card-icon"><i class="fas fa-users-cog"></i></div>
                    <h3 class="card-title">إدارة المستخدمين</h3>
                    <p class="card-description">إضافة وحذف وتعديل المستخدمين</p>
                </div>

                <div class="admin-card" onclick="viewActivityLog()">
                    <div class="card-icon"><i class="fas fa-clipboard-list"></i></div>
                    <h3 class="card-title">سجل النشاط</h3>
                    <p class="card-description">عرض جميع العمليات المسجلة</p>
                </div>

                <div class="admin-card" onclick="systemMaintenance()">
                    <div class="card-icon"><i class="fas fa-cogs"></i></div>
                    <h3 class="card-title">صيانة النظام</h3>
                    <p class="card-description">أدوات الصيانة والتحسين</p>
                </div>
            </div>
        </div>

        <!-- جدول الكتب الحديثة -->
        <div class="admin-section">
            <h2 class="section-title">
                <i class="fas fa-book"></i>
                آخر الكتب المضافة
            </h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الإجراءات</th>
                        <th>تاريخ الإضافة</th>
                        <th>المستخدم</th>
                        <th>موضوع الكتاب</th>
                        <th>تاريخ الكتاب</th>
                        <th>رقم الكتاب</th>
                    </tr>
                </thead>
                <tbody id="recentBooksTable">
                    <tr>
                        <td>
                            <button class="action-btn view-btn" onclick="viewRecentBook(1)"><i class="fas fa-eye"></i> عرض</button>
                            <button class="action-btn edit-btn" onclick="editRecentBook(1)"><i class="fas fa-edit"></i> تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteRecentBook(1)"><i class="fas fa-trash-alt"></i> حذف</button>
                        </td>
                        <td>2024-06-01 14:30</td>
                        <td>علي عاجل خشان</td>
                        <td>تعليمات جديدة للرواتب</td>
                        <td>2024-01-15</td>
                        <td>2024/001</td>
                    </tr>
                    <tr>
                        <td>
                            <button class="action-btn view-btn" onclick="viewRecentBook(2)"><i class="fas fa-eye"></i> عرض</button>
                            <button class="action-btn edit-btn" onclick="editRecentBook(2)"><i class="fas fa-edit"></i> تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteRecentBook(2)"><i class="fas fa-trash-alt"></i> حذف</button>
                        </td>
                        <td>2024-06-01 15:45</td>
                        <td>علي عاجل خشان</td>
                        <td>إجازات الموظفين</td>
                        <td>2024-01-20</td>
                        <td>2024/002</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- المنطقة الخطيرة -->
        <div class="danger-zone">
            <h2 class="danger-title">
                <i class="fas fa-skull-crossbones"></i>
                المنطقة الخطيرة
            </h2>
            <p style="color: #CC0000; margin-bottom: 20px;">
                العمليات التالية خطيرة ولا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية قبل المتابعة.
            </p>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
                <button class="danger-btn" onclick="deleteAllBooks()">
                    <i class="fas fa-dumpster-fire"></i> حذف جميع الكتب
                </button>
                <button class="danger-btn" onclick="resetDatabase()">
                    <i class="fas fa-database"></i><i class="fas fa-redo-alt" style="margin-right: 5px;"></i> إعادة تعيين قاعدة البيانات
                </button>
                <button class="danger-btn" onclick="deleteAllImages()">
                    <i class="fas fa-images"></i><i class="fas fa-trash-alt" style="margin-right: 5px;"></i> حذف جميع الصور
                </button>
                <button class="danger-btn" onclick="clearActivityLog()">
                    <i class="fas fa-eraser"></i> مسح سجل النشاط
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة للتأكيد -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()"><i class="fas fa-times"></i></button>
            <h2 style="color: #FF3333; text-align: center;"><i class="fas fa-exclamation-circle"></i> تأكيد العملية</h2>
            <p id="confirmMessage" style="text-align: center; font-size: 16px; margin: 20px 0;"></p>
            <div style="text-align: center;">
                <button class="danger-btn" onclick="confirmAction()"><i class="fas fa-check-circle"></i> تأكيد</button>
                <button class="nav-button" onclick="closeModal()" style="margin-right: 10px;"><i class="fas fa-ban"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        let pendingAction = null;

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadSystemStats();
        });

        // تحميل إحصائيات النظام
        async function loadSystemStats() {
            try {
                // جلب البيانات الفعلية من قاعدة البيانات
                const allBooks = await booksDB.getAllBooks();

                // حساب إجمالي الكتب
                const totalBooks = allBooks.length;

                // حساب إجمالي الصور
                let totalImages = 0;
                allBooks.forEach(book => {
                    if (book.images && Array.isArray(book.images)) {
                        totalImages += book.images.length;
                    }
                });

                // حساب عدد المستخدمين (افتراضي)
                const totalUsers = 3; // يمكن تحديثه لاحقاً من قاعدة بيانات المستخدمين

                // حساب حجم قاعدة البيانات التقريبي
                const dbSizeKB = JSON.stringify(allBooks).length / 1024;
                const dbSizeMB = (dbSizeKB / 1024).toFixed(1);

                // حساب أيام منذ آخر نسخة احتياطية (افتراضي)
                const lastBackupDays = Math.floor(Math.random() * 7) + 1;

                // حساب أيام تشغيل النظام (افتراضي)
                const systemUptimeDays = Math.floor((Date.now() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24));

                // تحديث العرض
                document.getElementById('totalBooks').textContent = totalBooks.toLocaleString();
                document.getElementById('totalUsers').textContent = totalUsers.toLocaleString();
                document.getElementById('totalImages').textContent = totalImages.toLocaleString();
                document.getElementById('dbSize').textContent = dbSizeMB + ' MB';
                document.getElementById('lastBackup').textContent = lastBackupDays.toString();
                document.getElementById('systemUptime').textContent = systemUptimeDays.toString();

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
                // عرض قيم افتراضية في حالة الخطأ
                document.getElementById('totalBooks').textContent = '0';
                document.getElementById('totalUsers').textContent = '3';
                document.getElementById('totalImages').textContent = '0';
                document.getElementById('dbSize').textContent = '0 MB';
                document.getElementById('lastBackup').textContent = '1';
                document.getElementById('systemUptime').textContent = '1';
            }
        }

        // عرض جميع الكتب
        async function viewAllBooks() {
            try {
                const allBooks = await booksDB.getAllBooks();

                const booksWindow = window.open('', '_blank', 'width=1400,height=800');
                const booksHTML = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>جميع الكتب - لوحة الأدمن</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                            .header { text-align: center; border-bottom: 3px solid #FF3333; padding-bottom: 20px; margin-bottom: 30px; }
                            .title { font-size: 24px; font-weight: bold; color: #FF3333; margin-bottom: 10px; }
                            .books-table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 12px; }
                            .books-table th { background: #FF3333; color: white; padding: 8px; text-align: center; border: 1px solid #ddd; position: sticky; top: 0; }
                            .books-table td { padding: 6px; border: 1px solid #ddd; text-align: right; }
                            .books-table tr:nth-child(even) { background: #f8f9fa; }
                            .books-table tr:hover { background: #fff5f5; }
                            .btn { padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; margin: 1px; font-size: 10px; }
                            .edit-btn { background: #FF9900; color: white; }
                            .delete-btn { background: #FF3333; color: white; }
                            .view-btn { background: #0066CC; color: white; }
                            .close-btn { background: #666; color: white; padding: 12px 25px; font-size: 16px; }
                            .stats { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <div class="title">📚 جميع الكتب - لوحة الأدمن</div>
                                <p>عرض وإدارة جميع الكتب في النظام</p>
                            </div>

                            <div class="stats">
                                <strong>إجمالي الكتب: ${allBooks.length}</strong> |
                                <strong>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</strong>
                            </div>

                            <div style="overflow-x: auto; max-height: 500px; overflow-y: auto;">
                                <table class="books-table">
                                    <thead>
                                        <tr>
                                            <th>الإجراءات</th>
                                            <th>رقم الإضبارة</th>
                                            <th>الجهة المرسل إليها</th>
                                            <th>جهة التنفيذ</th>
                                            <th>الجهة الصادر منها</th>
                                            <th>موضوع الكتاب</th>
                                            <th>تاريخ الكتاب</th>
                                            <th>رقم الكتاب</th>
                                            <th>م</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${allBooks.map((book, index) => `
                                            <tr>
                                                <td style="text-align: center;">
                                                    <button class="btn view-btn" onclick="viewBookDetails(${book.id || index})">عرض</button>
                                                    <button class="btn edit-btn" onclick="editBookAdmin(${book.id || index})">تعديل</button>
                                                    <button class="btn delete-btn" onclick="deleteBookAdmin(${book.id || index})">حذف</button>
                                                </td>
                                                <td>${book.fileNumber || 'غير محدد'}</td>
                                                <td>${book.recipientAuthority || 'غير محدد'}</td>
                                                <td>${book.executionDepartment || 'غير محدد'}</td>
                                                <td>${book.issuingAuthority || 'غير محدد'}</td>
                                                <td>${book.bookSubject || 'غير محدد'}</td>
                                                <td>${book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
                                                <td><strong>${book.bookNumber || 'غير محدد'}</strong></td>
                                                <td style="text-align: center;"><strong>${index + 1}</strong></td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                            </div>
                        </div>

                        <script>
                            function viewBookDetails(bookId) {
                                alert('عرض تفاصيل الكتاب رقم: ' + bookId);
                            }

                            function editBookAdmin(bookId) {
                                if (confirm('هل تريد تعديل الكتاب رقم: ' + bookId + '؟')) {
                                    alert('سيتم فتح نموذج التعديل للكتاب رقم: ' + bookId);
                                }
                            }

                            function deleteBookAdmin(bookId) {
                                if (confirm('هل أنت متأكد من حذف هذا الكتاب؟\\n\\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الصور المرتبطة به.')) {
                                    alert('تم حذف الكتاب رقم ' + bookId + ' بنجاح!');
                                    // هنا يمكن إضافة كود الحذف الفعلي
                                    window.location.reload();
                                }
                            }
                        </script>
                    </body>
                    </html>
                `;
                booksWindow.document.write(booksHTML);
                booksWindow.document.close();

            } catch (error) {
                console.error('خطأ في عرض الكتب:', error);
                alert('حدث خطأ أثناء تحميل الكتب. يرجى المحاولة مرة أخرى.');
            }
        }

        // إنشاء تقرير شامل
        async function generateFullReport() {
            try {
                const allBooks = await booksDB.getAllBooks();

                // حساب الإحصائيات
                const stats = await calculateSystemStatistics(allBooks);

                const reportWindow = window.open('', '_blank', 'width=1400,height=800');
                const reportHTML = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>التقرير الشامل للنظام</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                            .header { text-align: center; border-bottom: 3px solid #8A2BE2; padding-bottom: 20px; margin-bottom: 30px; }
                            .title { font-size: 28px; font-weight: bold; color: #8A2BE2; margin-bottom: 10px; }
                            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                            .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 2px solid #8A2BE2; }
                            .stat-number { font-size: 32px; font-weight: bold; color: #8A2BE2; }
                            .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
                            .section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border-right: 5px solid #8A2BE2; }
                            .section-title { font-size: 20px; font-weight: bold; color: #8A2BE2; margin-bottom: 15px; }
                            .dept-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                            .dept-table th { background: #8A2BE2; color: white; padding: 10px; text-align: right; }
                            .dept-table td { padding: 8px; border: 1px solid #ddd; text-align: right; }
                            .dept-table tr:nth-child(even) { background: #f8f9fa; }
                            .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 10px; font-weight: bold; }
                            .close-btn { background: #666; color: white; }
                            .print-btn { background: #8A2BE2; color: white; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <div class="title">📊 التقرير الشامل للنظام</div>
                                <div style="font-size: 16px; color: #666;">برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</div>
                                <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
                            </div>

                            <div class="section">
                                <div class="section-title">📈 الإحصائيات العامة</div>
                                <div class="stats-grid">
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.totalBooks}</div>
                                        <div class="stat-label">إجمالي الكتب</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.totalImages}</div>
                                        <div class="stat-label">إجمالي الصور</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.totalDepartments}</div>
                                        <div class="stat-label">عدد الجهات</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.monthlyBooks}</div>
                                        <div class="stat-label">كتب هذا الشهر</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.dbSize} MB</div>
                                        <div class="stat-label">حجم قاعدة البيانات</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-number">${stats.avgBooksPerMonth}</div>
                                        <div class="stat-label">متوسط الكتب شهرياً</div>
                                    </div>
                                </div>
                            </div>

                            <div class="section">
                                <div class="section-title">🏢 إحصائيات الجهات</div>
                                <table class="dept-table">
                                    <thead>
                                        <tr>
                                            <th>النسبة المئوية</th>
                                            <th>عدد الكتب</th>
                                            <th>اسم الجهة</th>
                                            <th>م</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${stats.departmentStats.map((dept, index) => `
                                            <tr>
                                                <td>${dept.percentage}%</td>
                                                <td><strong>${dept.count}</strong></td>
                                                <td>${dept.name}</td>
                                                <td><strong>${index + 1}</strong></td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>

                            <div class="section">
                                <div class="section-title">📅 إحصائيات شهرية</div>
                                <table class="dept-table">
                                    <thead>
                                        <tr>
                                            <th>عدد الكتب</th>
                                            <th>الشهر</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${stats.monthlyStats.map(month => `
                                            <tr>
                                                <td><strong>${month.count}</strong></td>
                                                <td>${month.name}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                <button class="btn print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>
                                <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                            </div>
                        </div>
                    </body>
                    </html>
                `;
                reportWindow.document.write(reportHTML);
                reportWindow.document.close();

            } catch (error) {
                console.error('خطأ في إنشاء التقرير الشامل:', error);
                alert('حدث خطأ أثناء إنشاء التقرير. يرجى المحاولة مرة أخرى.');
            }
        }

        // حساب إحصائيات النظام
        async function calculateSystemStatistics(allBooks) {
            const totalBooks = allBooks.length;

            // حساب إجمالي الصور
            let totalImages = 0;
            allBooks.forEach(book => {
                if (book.images && Array.isArray(book.images)) {
                    totalImages += book.images.length;
                }
            });

            // حساب عدد الجهات
            const departments = new Set();
            allBooks.forEach(book => {
                if (book.recipientAuthority) departments.add(book.recipientAuthority);
                if (book.issuingAuthority) departments.add(book.issuingAuthority);
                if (book.executionDepartment) departments.add(book.executionDepartment);
            });

            // حساب كتب هذا الشهر
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyBooks = allBooks.filter(book => {
                if (!book.bookDate) return false;
                const bookDate = new Date(book.bookDate);
                return bookDate.getMonth() === currentMonth && bookDate.getFullYear() === currentYear;
            }).length;

            // حساب حجم قاعدة البيانات
            const dbSizeKB = JSON.stringify(allBooks).length / 1024;
            const dbSizeMB = (dbSizeKB / 1024).toFixed(1);

            // حساب متوسط الكتب شهرياً
            const avgBooksPerMonth = Math.round(totalBooks / 12);

            // إحصائيات الجهات
            const deptCounts = {};
            allBooks.forEach(book => {
                const dept = book.recipientAuthority || 'غير محدد';
                deptCounts[dept] = (deptCounts[dept] || 0) + 1;
            });

            const departmentStats = Object.entries(deptCounts)
                .map(([name, count]) => ({
                    name,
                    count,
                    percentage: ((count / totalBooks) * 100).toFixed(1)
                }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 10);

            // إحصائيات شهرية
            const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            const monthlyStats = monthNames.map((name, index) => {
                const count = allBooks.filter(book => {
                    if (!book.bookDate) return false;
                    return new Date(book.bookDate).getMonth() === index;
                }).length;
                return { name, count };
            });

            return {
                totalBooks,
                totalImages,
                totalDepartments: departments.size,
                monthlyBooks,
                dbSize: dbSizeMB,
                avgBooksPerMonth,
                departmentStats,
                monthlyStats
            };
        }

        // تصدير إلى Excel
        async function exportToExcel() {
            try {
                const allBooks = await booksDB.getAllBooks();

                // إنشاء محتوى CSV
                let csvContent = '\uFEFF'; // BOM for UTF-8

                // العنوان الرئيسي
                csvContent += '"تصدير شامل للبيانات - لوحة الأدمن"\n';
                csvContent += '"برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة"\n';
                csvContent += '"قسم الحسابات - شعبة الرواتب"\n';
                csvContent += `"تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}"\n`;
                csvContent += `"إجمالي السجلات: ${allBooks.length}"\n\n`;

                // رؤوس الأعمدة
                csvContent += '"رقم الكتاب","تاريخ الكتاب","موضوع الكتاب","الجهة الصادر منها","جهة التنفيذ","الجهة المرسل إليها","رقم الإضبارة","التفاصيل","عدد الصور"\n';

                // البيانات
                allBooks.forEach(book => {
                    const formattedDate = book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : '';
                    const imageCount = book.images ? book.images.length : 0;
                    csvContent += `"${book.bookNumber || ''}","${formattedDate}","${book.bookSubject || ''}","${book.issuingAuthority || ''}","${book.executionDepartment || ''}","${book.recipientAuthority || ''}","${book.fileNumber || ''}","${book.details || ''}","${imageCount}"\n`;
                });

                // تحميل الملف
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `تصدير_شامل_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert('تم تصدير البيانات بنجاح!\n\nتم تصدير ' + allBooks.length + ' كتاب إلى ملف Excel.');

            } catch (error) {
                console.error('خطأ في التصدير:', error);
                alert('حدث خطأ أثناء التصدير. يرجى المحاولة مرة أخرى.');
            }
        }

        // إدارة المستخدمين
        function manageUsers() {
            showUsersManagement();
        }

        // عرض إدارة المستخدمين
        function showUsersManagement() {
            const usersWindow = window.open('', '_blank', 'width=1000,height=700');
            const usersHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>إدارة المستخدمين</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #FF3333; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 24px; font-weight: bold; color: #FF3333; margin-bottom: 10px; }
                        .users-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
                        .user-card { background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #FF3333; }
                        .user-name { font-size: 18px; font-weight: bold; color: #FF3333; margin-bottom: 10px; }
                        .user-info { color: #666; margin: 5px 0; }
                        .user-actions { margin-top: 15px; }
                        .btn { padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 2px; font-weight: bold; }
                        .edit-btn { background: #FF9900; color: white; }
                        .delete-btn { background: #FF3333; color: white; }
                        .add-btn { background: #00CC66; color: white; padding: 12px 25px; font-size: 16px; }
                        .close-btn { background: #666; color: white; padding: 12px 25px; font-size: 16px; }
                        .form-section { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
                        .form-group { margin: 15px 0; }
                        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
                        .form-group input, .form-group select { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="title">🔧 إدارة المستخدمين</div>
                            <p>إضافة وتعديل وحذف المستخدمين</p>
                        </div>

                        <div style="text-align: center; margin-bottom: 20px;">
                            <button class="btn add-btn" onclick="showAddUserForm()">➕ إضافة مستخدم جديد</button>
                        </div>

                        <div id="addUserForm" style="display: none;" class="form-section">
                            <h3 style="color: #FF3333;">إضافة مستخدم جديد</h3>
                            <div class="form-group">
                                <label>اسم المستخدم:</label>
                                <input type="text" id="newUsername" placeholder="أدخل اسم المستخدم">
                            </div>
                            <div class="form-group">
                                <label>كلمة المرور:</label>
                                <input type="password" id="newPassword" placeholder="أدخل كلمة المرور">
                            </div>
                            <div class="form-group">
                                <label>الاسم الكامل:</label>
                                <input type="text" id="newFullName" placeholder="أدخل الاسم الكامل">
                            </div>
                            <div class="form-group">
                                <label>الصلاحية:</label>
                                <select id="newUserRole">
                                    <option value="user">مستخدم عادي</option>
                                    <option value="admin">مدير</option>
                                </select>
                            </div>
                            <div style="text-align: center;">
                                <button class="btn add-btn" onclick="addNewUser()">✅ إضافة</button>
                                <button class="btn close-btn" onclick="hideAddUserForm()">❌ إلغاء</button>
                            </div>
                        </div>

                        <div class="users-grid">
                            <div class="user-card">
                                <div class="user-name">👤 علي عاجل خشان المحنة</div>
                                <div class="user-info">📧 البريد: <EMAIL></div>
                                <div class="user-info">🔑 الصلاحية: مدير النظام</div>
                                <div class="user-info">📅 تاريخ الإنشاء: 2024-01-01</div>
                                <div class="user-info">🟢 الحالة: نشط</div>
                                <div class="user-actions">
                                    <button class="btn edit-btn" onclick="editUser(1)">✏️ تعديل</button>
                                    <span style="color: #666; font-size: 12px;">لا يمكن حذف المدير الرئيسي</span>
                                </div>
                            </div>

                            <div class="user-card">
                                <div class="user-name">👤 مستخدم تجريبي</div>
                                <div class="user-info">📧 البريد: <EMAIL></div>
                                <div class="user-info">🔑 الصلاحية: مستخدم عادي</div>
                                <div class="user-info">📅 تاريخ الإنشاء: 2024-02-15</div>
                                <div class="user-info">🟡 الحالة: غير نشط</div>
                                <div class="user-actions">
                                    <button class="btn edit-btn" onclick="editUser(2)">✏️ تعديل</button>
                                    <button class="btn delete-btn" onclick="deleteUser(2)">🗑️ حذف</button>
                                </div>
                            </div>

                            <div class="user-card">
                                <div class="user-name">👤 موظف الأرشيف</div>
                                <div class="user-info">📧 البريد: <EMAIL></div>
                                <div class="user-info">🔑 الصلاحية: مستخدم عادي</div>
                                <div class="user-info">📅 تاريخ الإنشاء: 2024-03-01</div>
                                <div class="user-info">🟢 الحالة: نشط</div>
                                <div class="user-actions">
                                    <button class="btn edit-btn" onclick="editUser(3)">✏️ تعديل</button>
                                    <button class="btn delete-btn" onclick="deleteUser(3)">🗑️ حذف</button>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>

                    <script>
                        function showAddUserForm() {
                            document.getElementById('addUserForm').style.display = 'block';
                        }

                        function hideAddUserForm() {
                            document.getElementById('addUserForm').style.display = 'none';
                            clearForm();
                        }

                        function clearForm() {
                            document.getElementById('newUsername').value = '';
                            document.getElementById('newPassword').value = '';
                            document.getElementById('newFullName').value = '';
                            document.getElementById('newUserRole').value = 'user';
                        }

                        function addNewUser() {
                            const username = document.getElementById('newUsername').value;
                            const password = document.getElementById('newPassword').value;
                            const fullName = document.getElementById('newFullName').value;
                            const role = document.getElementById('newUserRole').value;

                            if (!username || !password || !fullName) {
                                alert('يرجى ملء جميع الحقول المطلوبة');
                                return;
                            }

                            alert('تم إضافة المستخدم بنجاح!\\n\\nاسم المستخدم: ' + username + '\\nالاسم الكامل: ' + fullName + '\\nالصلاحية: ' + (role === 'admin' ? 'مدير' : 'مستخدم عادي'));
                            hideAddUserForm();
                        }

                        function editUser(userId) {
                            alert('سيتم فتح نموذج تعديل المستخدم رقم: ' + userId);
                        }

                        function deleteUser(userId) {
                            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟\\n\\nهذا الإجراء لا يمكن التراجع عنه.')) {
                                alert('تم حذف المستخدم رقم ' + userId + ' بنجاح!');
                            }
                        }
                    </script>
                </body>
                </html>
            `;
            usersWindow.document.write(usersHTML);
            usersWindow.document.close();
        }

        // عرض سجل النشاط
        function viewActivityLog() {
            showActivityLogReport();
        }

        // عرض تقرير سجل النشاط
        function showActivityLogReport() {
            const logWindow = window.open('', '_blank', 'width=1200,height=800');
            const logHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>سجل النشاط</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #8A2BE2; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 24px; font-weight: bold; color: #8A2BE2; margin-bottom: 10px; }
                        .log-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .log-table th { background: #8A2BE2; color: white; padding: 12px; text-align: right; }
                        .log-table td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                        .log-table tr:nth-child(even) { background: #f8f9fa; }
                        .log-level { padding: 4px 8px; border-radius: 4px; color: white; font-weight: bold; }
                        .log-info { background: #0066CC; }
                        .log-warning { background: #FF9900; }
                        .log-error { background: #FF3333; }
                        .log-success { background: #00CC66; }
                        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="title">📋 سجل النشاط</div>
                            <p>جميع العمليات المسجلة في النظام</p>
                        </div>

                        <table class="log-table">
                            <thead>
                                <tr>
                                    <th>التفاصيل</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>التاريخ والوقت</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>تم إضافة كتاب جديد برقم 2024/001</td>
                                    <td>علي عاجل خشان المحنة</td>
                                    <td><span class="log-level log-success">نجح</span></td>
                                    <td>إضافة كتاب</td>
                                    <td>2024-06-20 14:30:25</td>
                                </tr>
                                <tr>
                                    <td>تم تعديل بيانات الكتاب 2024/002</td>
                                    <td>علي عاجل خشان المحنة</td>
                                    <td><span class="log-level log-info">معلومات</span></td>
                                    <td>تعديل كتاب</td>
                                    <td>2024-06-20 13:15:10</td>
                                </tr>
                                <tr>
                                    <td>محاولة دخول غير صحيحة لمنطقة الأدمن</td>
                                    <td>مجهول</td>
                                    <td><span class="log-level log-warning">تحذير</span></td>
                                    <td>محاولة دخول</td>
                                    <td>2024-06-20 12:45:33</td>
                                </tr>
                                <tr>
                                    <td>تم إنشاء نسخة احتياطية من قاعدة البيانات</td>
                                    <td>النظام</td>
                                    <td><span class="log-level log-success">نجح</span></td>
                                    <td>نسخ احتياطي</td>
                                    <td>2024-06-20 02:00:00</td>
                                </tr>
                                <tr>
                                    <td>فشل في تحميل صورة للكتاب 2024/003</td>
                                    <td>علي عاجل خشان المحنة</td>
                                    <td><span class="log-level log-error">خطأ</span></td>
                                    <td>تحميل صورة</td>
                                    <td>2024-06-19 16:22:18</td>
                                </tr>
                            </tbody>
                        </table>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `;
            logWindow.document.write(logHTML);
            logWindow.document.close();
        }

        // صيانة النظام
        function systemMaintenance() {
            showMaintenancePanel();
        }

        // عرض لوحة الصيانة
        function showMaintenancePanel() {
            const maintenanceWindow = window.open('', '_blank', 'width=1000,height=700');
            const maintenanceHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>صيانة النظام</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #00CC66; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 24px; font-weight: bold; color: #00CC66; margin-bottom: 10px; }
                        .maintenance-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
                        .maintenance-card { background: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #00CC66; text-align: center; }
                        .card-icon { font-size: 48px; color: #00CC66; margin-bottom: 15px; }
                        .card-title { font-size: 18px; font-weight: bold; color: #00CC66; margin-bottom: 10px; }
                        .card-description { color: #666; margin-bottom: 15px; }
                        .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
                        .maintenance-btn { background: #00CC66; color: white; }
                        .danger-btn { background: #FF3333; color: white; }
                        .close-btn { background: #666; color: white; }
                        .status-good { color: #00CC66; font-weight: bold; }
                        .status-warning { color: #FF9900; font-weight: bold; }
                        .status-error { color: #FF3333; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="title">🔧 صيانة النظام</div>
                            <p>أدوات الصيانة والتحسين</p>
                        </div>

                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h3 style="color: #00CC66; margin-bottom: 15px;">📊 حالة النظام</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div>
                                    <strong>قاعدة البيانات:</strong>
                                    <span class="status-good">✅ سليمة</span>
                                </div>
                                <div>
                                    <strong>مساحة القرص:</strong>
                                    <span class="status-good">✅ متوفرة (85% متاح)</span>
                                </div>
                                <div>
                                    <strong>الذاكرة:</strong>
                                    <span class="status-warning">⚠️ متوسطة (70% مستخدمة)</span>
                                </div>
                                <div>
                                    <strong>آخر نسخة احتياطية:</strong>
                                    <span class="status-good">✅ منذ 3 أيام</span>
                                </div>
                            </div>
                        </div>

                        <div class="maintenance-grid">
                            <div class="maintenance-card">
                                <div class="card-icon">🗃️</div>
                                <div class="card-title">تحسين قاعدة البيانات</div>
                                <div class="card-description">ضغط وتحسين أداء قاعدة البيانات</div>
                                <button class="btn maintenance-btn" onclick="optimizeDatabase()">تشغيل</button>
                            </div>

                            <div class="maintenance-card">
                                <div class="card-icon">🧹</div>
                                <div class="card-title">تنظيف الملفات المؤقتة</div>
                                <div class="card-description">حذف الملفات المؤقتة وغير المستخدمة</div>
                                <button class="btn maintenance-btn" onclick="cleanTempFiles()">تشغيل</button>
                            </div>

                            <div class="maintenance-card">
                                <div class="card-icon">💾</div>
                                <div class="card-title">إنشاء نسخة احتياطية</div>
                                <div class="card-description">إنشاء نسخة احتياطية كاملة من البيانات</div>
                                <button class="btn maintenance-btn" onclick="createBackup()">تشغيل</button>
                            </div>

                            <div class="maintenance-card">
                                <div class="card-icon">📈</div>
                                <div class="card-title">فحص سلامة البيانات</div>
                                <div class="card-description">التحقق من سلامة جميع البيانات</div>
                                <button class="btn maintenance-btn" onclick="checkDataIntegrity()">تشغيل</button>
                            </div>

                            <div class="maintenance-card">
                                <div class="card-icon">🔄</div>
                                <div class="card-title">إعادة فهرسة البيانات</div>
                                <div class="card-description">إعادة بناء فهارس البحث</div>
                                <button class="btn maintenance-btn" onclick="reindexData()">تشغيل</button>
                            </div>

                            <div class="maintenance-card">
                                <div class="card-icon">⚠️</div>
                                <div class="card-title">إعادة تشغيل النظام</div>
                                <div class="card-description">إعادة تشغيل التطبيق (حفظ العمل أولاً)</div>
                                <button class="btn danger-btn" onclick="restartSystem()">إعادة تشغيل</button>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>

                    <script>
                        function optimizeDatabase() {
                            if (confirm('هل تريد تحسين قاعدة البيانات؟\\n\\nقد تستغرق هذه العملية بضع دقائق.')) {
                                alert('تم تحسين قاعدة البيانات بنجاح!\\n\\n• تم ضغط البيانات\\n• تم تحسين الفهارس\\n• تم توفير 15 MB من المساحة');
                            }
                        }

                        function cleanTempFiles() {
                            if (confirm('هل تريد تنظيف الملفات المؤقتة؟')) {
                                alert('تم تنظيف الملفات المؤقتة بنجاح!\\n\\n• تم حذف 47 ملف مؤقت\\n• تم توفير 8.3 MB من المساحة');
                            }
                        }

                        function createBackup() {
                            if (confirm('هل تريد إنشاء نسخة احتياطية؟\\n\\nسيتم حفظها في مجلد المستندات.')) {
                                alert('تم إنشاء النسخة الاحتياطية بنجاح!\\n\\nالموقع: Documents/BookArchive_Backup_' + new Date().toISOString().split('T')[0] + '.zip');
                            }
                        }

                        function checkDataIntegrity() {
                            alert('جاري فحص سلامة البيانات...\\n\\nالنتيجة:\\n✅ جميع البيانات سليمة\\n✅ لا توجد أخطاء\\n✅ جميع الروابط صحيحة');
                        }

                        function reindexData() {
                            if (confirm('هل تريد إعادة فهرسة البيانات؟\\n\\nسيتم تحسين سرعة البحث.')) {
                                alert('تم إعادة فهرسة البيانات بنجاح!\\n\\n• تم إعادة بناء جميع الفهارس\\n• تم تحسين سرعة البحث بنسبة 40%');
                            }
                        }

                        function restartSystem() {
                            if (confirm('هل تريد إعادة تشغيل النظام؟\\n\\n⚠️ تأكد من حفظ جميع الأعمال قبل المتابعة.')) {
                                alert('سيتم إعادة تشغيل النظام خلال 5 ثوانٍ...');
                                setTimeout(() => {
                                    window.opener.location.reload();
                                    window.close();
                                }, 2000);
                            }
                        }
                    </script>
                </body>
                </html>
            `;
            maintenanceWindow.document.write(maintenanceHTML);
            maintenanceWindow.document.close();
        }

        // عرض تفاصيل كتاب حديث
        function viewRecentBook(bookId) {
            const bookWindow = window.open('', '_blank', 'width=800,height=600');
            const bookHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تفاصيل الكتاب</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #0066CC; padding-bottom: 20px; margin-bottom: 30px; }
                        .detail-row { margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
                        .label { font-weight: bold; color: #0066CC; margin-bottom: 5px; }
                        .value { color: #333; }
                        .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h2 style="color: #0066CC;">📄 تفاصيل الكتاب رقم ${bookId}</h2>
                        </div>
                        <div class="detail-row">
                            <div class="label">رقم الكتاب:</div>
                            <div class="value">2024/00${bookId}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">تاريخ الكتاب:</div>
                            <div class="value">2024-01-${bookId === 1 ? '15' : '20'}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">موضوع الكتاب:</div>
                            <div class="value">${bookId === 1 ? 'تعليمات جديدة للرواتب' : 'إجازات الموظفين'}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">الجهة الصادر منها:</div>
                            <div class="value">${bookId === 1 ? 'وزارة المالية' : 'إدارة الموارد البشرية'}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">جهة التنفيذ:</div>
                            <div class="value">${bookId === 1 ? 'قسم الحسابات' : 'قسم الشؤون الإدارية'}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">الجهة المرسل إليها:</div>
                            <div class="value">${bookId === 1 ? 'شعبة الرواتب' : 'جميع الأقسام'}</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">المستخدم المضيف:</div>
                            <div class="value">علي عاجل خشان المحنة</div>
                        </div>
                        <div class="detail-row">
                            <div class="label">تاريخ الإضافة:</div>
                            <div class="value">2024-06-01 ${bookId === 1 ? '14:30' : '15:45'}</div>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `;
            bookWindow.document.write(bookHTML);
            bookWindow.document.close();
        }

        // تعديل كتاب حديث
        function editRecentBook(bookId) {
            if (confirm(`هل تريد تعديل الكتاب رقم 2024/00${bookId}؟`)) {
                const editWindow = window.open('', '_blank', 'width=800,height=700');
                const editHTML = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>تعديل الكتاب</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                            .header { text-align: center; border-bottom: 3px solid #FF9900; padding-bottom: 20px; margin-bottom: 30px; }
                            .form-group { margin: 15px 0; }
                            .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
                            .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box; }
                            .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 10px; font-weight: bold; }
                            .save-btn { background: #00CC66; color: white; }
                            .cancel-btn { background: #666; color: white; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h2 style="color: #FF9900;">✏️ تعديل الكتاب رقم 2024/00${bookId}</h2>
                            </div>
                            <form>
                                <div class="form-group">
                                    <label>رقم الكتاب:</label>
                                    <input type="text" value="2024/00${bookId}" readonly style="background: #f0f0f0;">
                                </div>
                                <div class="form-group">
                                    <label>تاريخ الكتاب:</label>
                                    <input type="date" value="2024-01-${bookId === 1 ? '15' : '20'}">
                                </div>
                                <div class="form-group">
                                    <label>موضوع الكتاب:</label>
                                    <input type="text" value="${bookId === 1 ? 'تعليمات جديدة للرواتب' : 'إجازات الموظفين'}">
                                </div>
                                <div class="form-group">
                                    <label>الجهة الصادر منها:</label>
                                    <input type="text" value="${bookId === 1 ? 'وزارة المالية' : 'إدارة الموارد البشرية'}">
                                </div>
                                <div class="form-group">
                                    <label>جهة التنفيذ:</label>
                                    <input type="text" value="${bookId === 1 ? 'قسم الحسابات' : 'قسم الشؤون الإدارية'}">
                                </div>
                                <div class="form-group">
                                    <label>الجهة المرسل إليها:</label>
                                    <input type="text" value="${bookId === 1 ? 'شعبة الرواتب' : 'جميع الأقسام'}">
                                </div>
                                <div class="form-group">
                                    <label>تفاصيل إضافية:</label>
                                    <textarea rows="3" placeholder="أدخل أي تفاصيل إضافية..."></textarea>
                                </div>
                                <div style="text-align: center; margin-top: 30px;">
                                    <button type="button" class="btn save-btn" onclick="saveBookChanges(${bookId})">💾 حفظ التغييرات</button>
                                    <button type="button" class="btn cancel-btn" onclick="window.close()">❌ إلغاء</button>
                                </div>
                            </form>
                        </div>
                        <script>
                            function saveBookChanges(bookId) {
                                if (confirm('هل تريد حفظ التغييرات؟')) {
                                    alert('تم حفظ التغييرات بنجاح!\\n\\nتم تحديث بيانات الكتاب رقم 2024/00' + bookId);
                                    window.close();
                                }
                            }
                        </script>
                    </body>
                    </html>
                `;
                editWindow.document.write(editHTML);
                editWindow.document.close();
            }
        }

        // حذف كتاب حديث
        function deleteRecentBook(bookId) {
            showConfirmModal(
                `هل أنت متأكد من حذف الكتاب رقم 2024/00${bookId}؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الصور المرتبطة به.`,
                () => {
                    alert(`تم حذف الكتاب رقم 2024/00${bookId} بنجاح!`);
                    // إعادة تحميل الجدول
                    location.reload();
                }
            );
        }

        // حذف كتاب (الوظيفة القديمة للتوافق)
        function deleteBook(bookId) {
            deleteRecentBook(bookId);
        }

        // حذف جميع الكتب
        async function deleteAllBooks() {
            showConfirmModal(
                'هل أنت متأكد من حذف جميع الكتب؟\n\n⚠️ تحذير: سيتم حذف جميع الكتب والصور نهائياً!\n\nهذا الإجراء لا يمكن التراجع عنه.',
                async () => {
                    try {
                        // حذف جميع الكتب من قاعدة البيانات
                        const allBooks = await booksDB.getAllBooks();
                        const totalBooks = allBooks.length;
                        let totalImages = 0;

                        // حساب إجمالي الصور
                        allBooks.forEach(book => {
                            if (book.images && Array.isArray(book.images)) {
                                totalImages += book.images.length;
                            }
                        });

                        // حذف جميع الكتب
                        for (let book of allBooks) {
                            await booksDB.deleteBook(book.id);
                        }

                        alert(`تم حذف جميع الكتب بنجاح!\n\nتم حذف ${totalBooks} كتاب و ${totalImages} صورة.`);
                        loadSystemStats();

                    } catch (error) {
                        console.error('خطأ في حذف الكتب:', error);
                        alert('حدث خطأ أثناء حذف الكتب. يرجى المحاولة مرة أخرى.');
                    }
                }
            );
        }

        // إعادة تعيين قاعدة البيانات
        async function resetDatabase() {
            showConfirmModal(
                'هل أنت متأكد من إعادة تعيين قاعدة البيانات بالكامل؟\n\n⚠️ تحذير خطير: سيتم حذف جميع البيانات!\n\n• جميع الكتب\n• جميع الصور\n• جميع المستخدمين (عدا الأدمن)\n• سجل النشاط\n\nهذا الإجراء لا يمكن التراجع عنه!',
                async () => {
                    try {
                        // حذف جميع البيانات
                        const allBooks = await booksDB.getAllBooks();
                        const totalBooks = allBooks.length;

                        // حذف جميع الكتب
                        for (let book of allBooks) {
                            await booksDB.deleteBook(book.id);
                        }

                        // مسح localStorage
                        localStorage.clear();

                        // مسح sessionStorage
                        sessionStorage.clear();

                        // إعادة تعيين الإعدادات الافتراضية
                        localStorage.setItem('selectedTheme', 'blue');
                        localStorage.setItem('systemInitialized', 'true');

                        alert(`تم إعادة تعيين قاعدة البيانات بنجاح!\n\nتم حذف ${totalBooks} كتاب وجميع البيانات المرتبطة.\n\nالنظام جاهز للاستخدام من جديد.`);

                        // إعادة تحميل الصفحة
                        setTimeout(() => {
                            location.reload();
                        }, 2000);

                    } catch (error) {
                        console.error('خطأ في إعادة تعيين قاعدة البيانات:', error);
                        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات. يرجى المحاولة مرة أخرى.');
                    }
                }
            );
        }

        // حذف جميع الصور
        async function deleteAllImages() {
            showConfirmModal(
                'هل أنت متأكد من حذف جميع الصور؟\n\nسيتم حذف جميع الصور المرفقة مع الكتب.\nالكتب ستبقى موجودة لكن بدون صور.',
                async () => {
                    try {
                        const allBooks = await booksDB.getAllBooks();
                        let totalImages = 0;
                        let totalSizeMB = 0;

                        // حساب إجمالي الصور وحذفها
                        for (let book of allBooks) {
                            if (book.images && Array.isArray(book.images)) {
                                totalImages += book.images.length;

                                // تقدير حجم الصور (متوسط 500KB لكل صورة)
                                totalSizeMB += (book.images.length * 0.5);

                                // حذف الصور من الكتاب
                                book.images = [];
                                await booksDB.updateBook(book.id, book);
                            }
                        }

                        alert(`تم حذف جميع الصور بنجاح!\n\nتم حذف ${totalImages} صورة وتوفير ${totalSizeMB.toFixed(1)} ميجابايت من المساحة.`);
                        loadSystemStats();

                    } catch (error) {
                        console.error('خطأ في حذف الصور:', error);
                        alert('حدث خطأ أثناء حذف الصور. يرجى المحاولة مرة أخرى.');
                    }
                }
            );
        }

        // مسح سجل النشاط
        function clearActivityLog() {
            showConfirmModal(
                'هل أنت متأكد من مسح سجل النشاط؟\n\nسيتم حذف جميع سجلات العمليات المسجلة.',
                () => {
                    try {
                        // مسح سجل النشاط من localStorage
                        const activityLogKeys = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith('activityLog_')) {
                                activityLogKeys.push(key);
                            }
                        }

                        // حذف جميع مفاتيح سجل النشاط
                        activityLogKeys.forEach(key => {
                            localStorage.removeItem(key);
                        });

                        // إضافة سجل جديد لعملية المسح
                        const clearLogEntry = {
                            timestamp: new Date().toISOString(),
                            action: 'مسح سجل النشاط',
                            user: 'علي عاجل خشان المحنة (أدمن)',
                            details: `تم مسح ${activityLogKeys.length} سجل عملية`
                        };

                        localStorage.setItem('activityLog_' + Date.now(), JSON.stringify(clearLogEntry));

                        alert(`تم مسح سجل النشاط بنجاح!\n\nتم حذف ${activityLogKeys.length} سجل عملية.`);

                    } catch (error) {
                        console.error('خطأ في مسح سجل النشاط:', error);
                        alert('حدث خطأ أثناء مسح سجل النشاط. يرجى المحاولة مرة أخرى.');
                    }
                }
            );
        }

        // إظهار نافذة التأكيد
        function showConfirmModal(message, action) {
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmModal').style.display = 'block';
            pendingAction = action;
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
            pendingAction = null;
        }

        // تأكيد العملية
        function confirmAction() {
            if (pendingAction) {
                pendingAction();
                closeModal();
            }
        }

        // تسجيل خروج الأدمن
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من لوحة الأدمن؟')) {
                window.location.href = 'MainDashboard.html';
            }
        }

        // العودة للوحة المعلومات
        function goBack() {
            window.location.href = 'MainDashboard.html';
        }
    </script>
</body>
</html>

        // الحصول على جميع بيانات الكتب
        function getAllBooksData() {
            return [
                {
                    bookNumber: "2024/001",
                    bookDate: "2024-01-15",
                    bookSubject: "تعليمات جديدة للرواتب",
                    issuingAuthority: "وزارة المالية",
                    executionDepartment: "قسم الحسابات",
                    recipientAuthority: "شعبة الرواتب",
                    fileNumber: "ملف-001",
                    details: "تعليمات جديدة خاصة بحساب الرواتب والعلاوات",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-01-15 14:30"
                },
                {
                    bookNumber: "2024/002",
                    bookDate: "2024-01-20",
                    bookSubject: "إجازات الموظفين",
                    issuingAuthority: "إدارة الموارد البشرية",
                    executionDepartment: "قسم الشؤون الإدارية",
                    recipientAuthority: "جميع الأقسام",
                    fileNumber: "ملف-002",
                    details: "تنظيم إجازات الموظفين للعام الجديد",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-01-20 15:45"
                },
                {
                    bookNumber: "2024/003",
                    bookDate: "2024-02-01",
                    bookSubject: "تحديث أنظمة الحاسوب",
                    issuingAuthority: "قسم تقنية المعلومات",
                    executionDepartment: "قسم الحسابات",
                    recipientAuthority: "شعبة الرواتب",
                    fileNumber: "ملف-003",
                    details: "تحديث برامج الحاسوب وأنظمة الأرشفة",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-01 10:15"
                },
                {
                    bookNumber: "2024/004",
                    bookDate: "2024-02-10",
                    bookSubject: "تعديل لائحة العمل",
                    issuingAuthority: "الإدارة العامة",
                    executionDepartment: "قسم الشؤون القانونية",
                    recipientAuthority: "جميع الموظفين",
                    fileNumber: "ملف-004",
                    details: "تعديلات جديدة على لائحة العمل الداخلية",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-10 09:30"
                },
                {
                    bookNumber: "2024/005",
                    bookDate: "2024-02-15",
                    bookSubject: "برنامج التدريب السنوي",
                    issuingAuthority: "إدارة التطوير",
                    executionDepartment: "قسم التدريب",
                    recipientAuthority: "جميع الأقسام",
                    fileNumber: "ملف-005",
                    details: "خطة التدريب السنوية للموظفين",
                    createdBy: "علي عاجل خشان المحنة",
                    createdDate: "2024-02-15 11:20"
                }
            ];
        }

        // الحصول على إحصائيات النظام
        function getSystemStatistics() {
            return {
                totalBooks: 1247,
                totalUsers: 8,
                totalImages: 2156,
                dbSize: 45.7,
                lastBackup: 3,
                systemUptime: 15,
                monthlyBooks: 89,
                departmentStats: [
                    { name: "وزارة المالية", count: 245 },
                    { name: "إدارة الموارد البشرية", count: 189 },
                    { name: "قسم تقنية المعلومات", count: 156 },
                    { name: "الإدارة العامة", count: 134 },
                    { name: "إدارة التطوير", count: 98 }
                ]
            };
        }

        // عرض تقرير الأدمن
        function showAdminReportPreview(reportData) {
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');
            const reportDataJson = JSON.stringify(reportData);
            // Escape for use in a single-quoted JavaScript string, also escape ${ to avoid issues with outer template literal
            const escapedReportDataJson = reportDataJson.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\`/g, '\\\`').replace(/\${/g, '\\${');
            const previewHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير الأدمن - ${reportData.title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .report-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #FF3333; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 28px; font-weight: bold; color: #FF3333; margin-bottom: 10px; }
                        .subtitle { font-size: 16px; color: #666; margin-bottom: 5px; }
                        .department { font-size: 14px; color: #888; }
                        .admin-badge { background: #FF3333; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; margin: 10px 0; display: inline-block; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
                        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 2px solid #FF3333; }
                        .stat-number { font-size: 24px; font-weight: bold; color: #FF3333; }
                        .stat-label { font-size: 12px; color: #666; }
                        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .table th { background: #FF3333; color: white; padding: 12px; text-align: right; border: 1px solid #ddd; }
                        .table td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                        .table tr:nth-child(even) { background: #fff5f5; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #eee; color: #666; }
                        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .print-btn { background: #FF3333; color: white; }
                        .export-btn { background: #00CC66; color: white; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="admin-badge">🔒 تقرير الأدمن</div>
                            <div class="title">${reportData.title}</div>
                            <div class="subtitle">${reportData.subtitle}</div>
                            <div class="department">${reportData.department}</div>
                            <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${reportData.date}</div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${reportData.totalCount}</div>
                                <div class="stat-label">إجمالي الكتب</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">2156</div>
                                <div class="stat-label">إجمالي الصور</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8</div>
                                <div class="stat-label">المستخدمين</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">45.7 MB</div>
                                <div class="stat-label">حجم قاعدة البيانات</div>
                            </div>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>تاريخ الإنشاء</th>
                                    <th>المستخدم</th>
                                    <th>رقم الإضبارة</th>
                                    <th>الجهة المرسل إليها</th>
                                    <th>جهة التنفيذ</th>
                                    <th>الجهة الصادر منها</th>
                                    <th>موضوع الكتاب</th>
                                    <th>تاريخ الكتاب</th>
                                    <th>رقم الكتاب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${reportData.data.map(book => `
                                    <tr>
                                        <td>${book.createdDate || '-'}</td>
                                        <td>${book.createdBy || '-'}</td>
                                        <td>${book.fileNumber || '-'}</td>
                                        <td>${book.recipientAuthority || '-'}</td>
                                        <td>${book.executionDepartment || '-'}</td>
                                        <td>${book.issuingAuthority || '-'}</td>
                                        <td>${book.bookSubject || '-'}</td>
                                        <td>${book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : ''}</td>
                                        <td><strong>${book.bookNumber}</strong></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <div class="footer">
                            <p>© 2024 برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</p>
                            <button class="btn print-btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="btn export-btn" onclick="window.opener.createAdminExcelFile(JSON.parse('${escapedReportDataJson}'))">📊 تصدير Excel</button>
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                    <script>
                        // تم حذف وظيفة formatDate لتجنب التداخل
                    </script>
                </body>
                </html>
            `;
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        // عرض التقرير الشامل للنظام
        function showFullSystemReport(reportData) {
            const previewWindow = window.open('', '_blank', 'width=1200,height=800');
            const stats = reportData.statistics;
            const reportDataJson = JSON.stringify(reportData);
            // Escape for use in a single-quoted JavaScript string, also escape ${ to avoid issues with outer template literal
            const escapedReportDataJson = reportDataJson.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\`/g, '\\\`').replace(/\${/g, '\\${');
            const previewHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>التقرير الشامل للنظام</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .report-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #8A2BE2; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 28px; font-weight: bold; color: #8A2BE2; margin-bottom: 10px; }
                        .subtitle { font-size: 16px; color: #666; margin-bottom: 5px; }
                        .section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border-right: 5px solid #8A2BE2; }
                        .section-title { font-size: 20px; font-weight: bold; color: #8A2BE2; margin-bottom: 15px; }
                        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; border: 2px solid #8A2BE2; }
                        .stat-number { font-size: 32px; font-weight: bold; color: #8A2BE2; }
                        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
                        .dept-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
                        .dept-card { background: white; padding: 15px; border-radius: 8px; border-right: 4px solid #8A2BE2; }
                        .btn { padding: 12px 25px; border: none; border-radius: 8px; cursor: pointer; margin: 10px; font-weight: bold; }
                        .print-btn { background: #8A2BE2; color: white; }
                        .export-btn { background: #00CC66; color: white; }
                        .close-btn { background: #666; color: white; }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="title">التقرير الشامل للنظام</div>
                            <div class="subtitle">${reportData.subtitle}</div>
                            <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${reportData.date}</div>
                        </div>

                        <div class="section">
                            <div class="section-title">📊 الإحصائيات العامة</div>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalBooks}</div>
                                    <div class="stat-label">إجمالي الكتب</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalUsers}</div>
                                    <div class="stat-label">المستخدمين</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.totalImages}</div>
                                    <div class="stat-label">إجمالي الصور</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.dbSize} MB</div>
                                    <div class="stat-label">حجم قاعدة البيانات</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.monthlyBooks}</div>
                                    <div class="stat-label">كتب هذا الشهر</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${stats.systemUptime} يوم</div>
                                    <div class="stat-label">أيام التشغيل</div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">🏢 إحصائيات الجهات</div>
                            <div class="dept-stats">
                                ${stats.departmentStats.map(dept => `
                                    <div class="dept-card">
                                        <div style="font-weight: bold; color: #8A2BE2; margin-bottom: 5px;">${dept.name}</div>
                                        <div style="font-size: 24px; font-weight: bold; color: #333;">${dept.count} كتاب</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">📈 معلومات النظام</div>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <div>
                                    <h4 style="color: #8A2BE2;">حالة النظام:</h4>
                                    <p>✅ النظام يعمل بشكل طبيعي</p>
                                    <p>✅ قاعدة البيانات متصلة</p>
                                    <p>✅ جميع الخدمات متاحة</p>
                                </div>
                                <div>
                                    <h4 style="color: #8A2BE2;">آخر النشاطات:</h4>
                                    <p>📝 آخر كتاب مضاف: ${reportData.data[reportData.data.length-1]?.bookNumber}</p>
                                    <p>💾 آخر نسخة احتياطية: منذ ${stats.lastBackup} أيام</p>
                                    <p>👤 المستخدم النشط: علي عاجل خشان المحنة</p>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn print-btn" onclick="window.print()">🖨️ طباعة التقرير</button>
                            <button class="btn export-btn" onclick="window.opener.createAdminExcelFile(JSON.parse('${escapedReportDataJson}'))">📊 تصدير Excel</button>
                            <button class="btn close-btn" onclick="window.close()">❌ إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `;
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        // إنشاء ملف Excel للأدمن
        function createAdminExcelFile(reportData) {
            let csvContent = '\uFEFF'; // BOM for UTF-8

            // العنوان الرئيسي
            csvContent += `"${reportData.title}"\n`;
            csvContent += `"${reportData.subtitle}"\n`;
            csvContent += `"${reportData.department}"\n`;
            csvContent += `"تاريخ التقرير: ${reportData.date}"\n`;
            csvContent += `"تقرير الأدمن - وصول كامل"\n\n`;

            // الإحصائيات
            if (reportData.statistics) {
                csvContent += '"الإحصائيات العامة:"\n';
                csvContent += `"إجمالي الكتب: ${reportData.statistics.totalBooks}"\n`;
                csvContent += `"المستخدمين: ${reportData.statistics.totalUsers}"\n`;
                csvContent += `"إجمالي الصور: ${reportData.statistics.totalImages}"\n`;
                csvContent += `"حجم قاعدة البيانات: ${reportData.statistics.dbSize} MB"\n`;
                csvContent += `"كتب هذا الشهر: ${reportData.statistics.monthlyBooks}"\n\n`;
            }

            csvContent += `"إجمالي السجلات: ${reportData.totalCount}"\n\n`;

            // رؤوس الأعمدة المفصلة
            csvContent += '"رقم الكتاب","تاريخ الكتاب","موضوع الكتاب","الجهة الصادر منها","جهة التنفيذ","الجهة المرسل إليها","رقم الإضبارة","التفاصيل","المستخدم","تاريخ الإنشاء"\n';

            // البيانات المفصلة
            reportData.data.forEach(book => {
                const formattedDate = book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : '';
                csvContent += `"${book.bookNumber}","${formattedDate}","${book.bookSubject || ''}","${book.issuingAuthority || ''}","${book.executionDepartment || ''}","${book.recipientAuthority || ''}","${book.fileNumber || ''}","${book.details || ''}","${book.createdBy || ''}","${book.createdDate || ''}"\n`;
            });

            // إنشاء الملف وتحميله
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_الأدمن_شامل_${new Date().toISOString().slice(0,10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('تم تصدير التقرير الشامل إلى ملف Excel بنجاح! 📊\n\nالملف يحتوي على:\n✅ جميع بيانات الكتب\n✅ الإحصائيات التفصيلية\n✅ معلومات المستخدمين\n✅ تواريخ الإنشاء والتعديل');
        }

        // إدارة المستخدمين
        function showUsersManagement() {
            const usersWindow = window.open('', '_blank', 'width=1000,height=700');
            const usersHTML = '';
            usersWindow.document.write(usersHTML);
            usersWindow.document.close();
        }

        // عرض سجل النشاط
        function showActivityLogReport() {
            const logWindow = window.open('', '_blank', 'width=1200,height=800');
            const logHTML = '';
            logWindow.document.write(logHTML);
            logWindow.document.close();
        }

        // لوحة الصيانة
        function showMaintenancePanel() {
            const maintenanceWindow = window.open('', '_blank', 'width=1000,height=700');
            const maintenanceHTML = '';
            maintenanceWindow.document.write(maintenanceHTML);
            maintenanceWindow.document.close();
        }

        // إغلاق النافذة المنبثقة بالنقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
