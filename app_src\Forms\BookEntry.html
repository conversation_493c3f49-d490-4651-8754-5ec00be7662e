<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدخال بيانات كتاب جديد - برنامج الأرشفة الإلكترونية</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 50%, #CCE7FF 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .header-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .navigation {
            background: white;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 102, 204, 0.4);
        }

        .form-container {
            max-width: 1000px;
            margin: 30px auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-title {
            font-size: 24px;
            font-weight: bold;
            color: #00CC66;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .required {
            color: #FF3333;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #00CC66;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #00FF80;
            box-shadow: 0 0 10px rgba(0, 204, 102, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .image-upload-section {
            background: #F8F9FA;
            border: 2px dashed #00CC66;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .image-upload-section:hover {
            border-color: #00FF80;
            background: #F0FFF0;
        }

        .upload-icon {
            font-size: 48px;
            color: #00CC66;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }

        .upload-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .upload-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 204, 102, 0.4);
        }

        .scanner-btn {
            background: linear-gradient(45deg, #FF9900, #FFAA33);
        }

        .scanner-btn:hover {
            box-shadow: 0 6px 20px rgba(255, 153, 0, 0.4);
        }

        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .preview-item {
            position: relative;
            border: 2px solid #00CC66;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .preview-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .preview-name {
            padding: 8px;
            font-size: 12px;
            background: #F8F9FA;
            text-align: center;
        }

        .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #FF3333;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #E9ECEF;
        }

        .save-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 204, 102, 0.4);
        }

        .save-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 204, 102, 0.6);
        }

        .clear-btn {
            background: linear-gradient(45deg, #666666, #888888);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                margin: 20px;
                padding: 20px;
            }
            
            .upload-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .form-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1 class="header-title">إدخال بيانات كتاب جديد</h1>
        <p>إضافة كتاب جديد إلى الأرشيف الإلكتروني</p>
    </header>

    <nav class="navigation">
        <button class="nav-button" onclick="goBack()">🔙 العودة للوحة المعلومات</button>
        <span>رقم الكتاب التالي: <strong id="nextBookId">1</strong></span>
        <button class="nav-button" onclick="showHelp()">❓ المساعدة</button>
    </nav>

    <div class="form-container">
        <h2 class="form-title">📝 نموذج إدخال بيانات الكتاب</h2>

        <form id="bookForm">
            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label" for="bookNumber">
                        رقم الكتاب <span class="required">*</span>
                    </label>
                    <input type="text" id="bookNumber" name="bookNumber" class="form-input" required>
                    <div class="help-text">رقم الكتاب مطلوب ولا يمكن الحفظ بدونه</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="bookDate">
                        تاريخ الكتاب <span class="required">*</span>
                    </label>
                    <input type="date" id="bookDate" name="bookDate" class="form-input" required placeholder="dd/mm/yyyy">
                    <div class="help-text">تاريخ الكتاب مطلوب ولا يمكن الحفظ بدونه</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="bookSubject">
                        موضوع الكتاب
                    </label>
                    <input type="text" id="bookSubject" name="bookSubject" class="form-input">
                    <div class="help-text">موضوع الكتاب (اختياري)</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="issuingAuthority">
                        الجهة الصادر منها الكتاب
                    </label>
                    <input type="text" id="issuingAuthority" name="issuingAuthority" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="executionDepartment">
                        جهة التنفيذ
                    </label>
                    <input type="text" id="executionDepartment" name="executionDepartment" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="recipientAuthority">
                        الجهة المرسل إليها الكتاب
                    </label>
                    <input type="text" id="recipientAuthority" name="recipientAuthority" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="fileNumber">
                        رقم إضبارة الحفظ
                    </label>
                    <input type="text" id="fileNumber" name="fileNumber" class="form-input">
                </div>

                <div class="form-group full-width">
                    <label class="form-label" for="details">
                        التفاصيل
                    </label>
                    <textarea id="details" name="details" class="form-input form-textarea" rows="4"></textarea>
                    <div class="help-text">تفاصيل إضافية عن الكتاب</div>
                </div>
            </div>

            <div class="form-group full-width">
                <label class="form-label">صور الكتاب</label>
                <div class="image-upload-section">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">إضافة صور للكتاب</div>
                    <div class="upload-buttons">
                        <button type="button" class="upload-btn" onclick="selectFiles()">
                            📁 اختيار من الكمبيوتر
                        </button>
                        <button type="button" class="upload-btn scanner-btn" onclick="scanDocument()">
                            🖨️ مسح ضوئي
                        </button>
                        <button type="button" class="upload-btn" onclick="showScannerInfo()" style="background: linear-gradient(45deg, #6666CC, #8888FF);">
                            ℹ️ معلومات الماسح
                        </button>
                        <button type="button" class="upload-btn" onclick="openSaveFolder()" style="background: linear-gradient(45deg, #FF6600, #FF8833);">
                            📁 فتح مجلد الحفظ
                        </button>
                        <button type="button" class="upload-btn" onclick="runScannerTest()" style="background: linear-gradient(45deg, #9966CC, #BB88FF);">
                            🧪 اختبار النظام
                        </button>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    <div class="help-text">
                        يمكن إضافة عدة صور للكتاب الواحد من خلال:
                        <br>• <strong>اختيار من الكمبيوتر:</strong> تحديد ملفات الصور المحفوظة
                        <br>• <strong>المسح الضوئي:</strong> مسح الوثائق مباشرة من الماسح الضوئي (يدعم المسح المتعدد)
                        <br>• <strong>معلومات الماسح:</strong> عرض الماسحات المتصلة وحالتها
                        <br><small>الصور ستحفظ باسم الموضوع + رقم متسلسل، والصور الممسوحة ستحفظ كملفات PDF</small>
                    </div>
                </div>

                <div id="imagePreview" class="image-preview"></div>
            </div>

            <div class="form-actions">
                <button type="submit" class="save-btn">
                    💾 حفظ الكتاب
                </button>
                <button type="button" class="clear-btn" onclick="clearForm()">
                    🗑️ مسح النموذج
                </button>
            </div>
        </form>
    </div>

    <script src="../Database/books_database.js"></script>
    <script src="../Scripts/scanner.js"></script>
    <script src="../Scripts/pdf-generator.js"></script>
    <script src="../Scripts/scanner-test.js"></script>
    <script>
        // إعداد Electron API
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            const path = require('path');
            const os = require('os');

            // إنشاء واجهة API للتواصل مع العملية الرئيسية
            window.electronAPI = {
                invoke: async (channel, ...args) => {
                    return await ipcRenderer.invoke(channel, ...args);
                },
                send: (channel, ...args) => {
                    ipcRenderer.send(channel, ...args);
                },
                on: (channel, callback) => {
                    ipcRenderer.on(channel, callback);
                }
            };
        }

        let uploadedImages = [];
        let imageCounter = 0;

        // تم نقل وظيفة loadNextBookId إلى الأسفل مع دعم قاعدة البيانات

        // اختيار الملفات
        function selectFiles() {
            document.getElementById('fileInput').click();
        }

        // التعامل مع اختيار الملفات
        function handleFileSelect(event) {
            const files = event.target.files;
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    addImageToPreview(file);
                }
            }
        }

        // إضافة صورة للمعاينة
        function addImageToPreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imageCounter++;
                const imageId = 'img_' + imageCounter;

                const imageItem = document.createElement('div');
                imageItem.className = 'preview-item';
                imageItem.id = imageId;

                // تحديد اسم الصورة بناءً على الموضوع
                const subject = document.getElementById('bookSubject').value || 'صورة';
                const imageName = uploadedImages.length === 0 ? subject : subject + '_' + String(uploadedImages.length).padStart(2, '0');

                imageItem.innerHTML = `
                    <img src="${e.target.result}" alt="صورة الكتاب" class="preview-image">
                    <div class="preview-name">${imageName}</div>
                    <button type="button" class="remove-image" onclick="removeImage('${imageId}')">×</button>
                `;

                document.getElementById('imagePreview').appendChild(imageItem);

                uploadedImages.push({
                    id: imageId,
                    file: file,
                    name: imageName,
                    data: e.target.result
                });
            };
            reader.readAsDataURL(file);
        }

        // إزالة صورة
        function removeImage(imageId) {
            const imageElement = document.getElementById(imageId);
            if (imageElement) {
                imageElement.remove();
                uploadedImages = uploadedImages.filter(img => img.id !== imageId);
            }
        }

        // مسح ضوئي محسن
        async function scanDocument() {
            try {
                // إظهار رسالة التحميل
                showScanningMessage('جاري التحقق من الماسح الضوئي...');

                // التحقق من وجود الماسح الضوئي
                const scannerAvailable = await scanner.checkScannerAvailability();

                if (!scannerAvailable) {
                    hideScanningMessage();
                    showErrorMessage('لم يتم العثور على ماسح ضوئي متصل. تأكد من توصيل الماسح وتثبيت التعريفات.');
                    return;
                }

                // سؤال المستخدم عن عدد الصفحات
                const pageCount = await askForPageCount();
                if (pageCount === null) {
                    hideScanningMessage();
                    return; // المستخدم ألغى العملية
                }

                // بدء عملية المسح
                updateScanningMessage('جاري بدء عملية المسح...');

                let scannedPages;
                if (pageCount === 1) {
                    // مسح صفحة واحدة
                    const scannedImage = await scanner.startScan();
                    scannedPages = [scannedImage];
                } else {
                    // مسح متعدد الصفحات
                    scannedPages = await scanner.scanMultiplePages(pageCount);
                }

                // معالجة الصور الممسوحة
                await processScannedImages(scannedPages);

                hideScanningMessage();
                showSuccessMessage(`تم مسح ${pageCount} صفحة بنجاح!`);

            } catch (error) {
                hideScanningMessage();
                console.error('خطأ في المسح الضوئي:', error);
                showErrorMessage('حدث خطأ أثناء المسح الضوئي: ' + error.message);
            }
        }

        // سؤال المستخدم عن عدد الصفحات
        async function askForPageCount() {
            return new Promise((resolve) => {
                const modal = createPageCountModal();
                document.body.appendChild(modal);

                const confirmBtn = modal.querySelector('#confirmPageCount');
                const cancelBtn = modal.querySelector('#cancelPageCount');
                const pageInput = modal.querySelector('#pageCountInput');

                confirmBtn.onclick = () => {
                    const count = parseInt(pageInput.value);
                    if (count > 0 && count <= 50) {
                        document.body.removeChild(modal);
                        resolve(count);
                    } else {
                        alert('يرجى إدخال عدد صفحات صحيح (1-50)');
                    }
                };

                cancelBtn.onclick = () => {
                    document.body.removeChild(modal);
                    resolve(null);
                };
            });
        }

        // إنشاء نافذة اختيار عدد الصفحات
        function createPageCountModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; min-width: 300px;">
                    <h3 style="color: #00CC66; margin-bottom: 20px;">🖨️ المسح الضوئي</h3>
                    <p style="margin-bottom: 20px;">كم عدد الصفحات التي تريد مسحها؟</p>
                    <input type="number" id="pageCountInput" value="1" min="1" max="50"
                           style="width: 100px; padding: 10px; border: 2px solid #00CC66; border-radius: 5px; text-align: center; font-size: 16px; margin-bottom: 20px;">
                    <div>
                        <button id="confirmPageCount" style="background: #00CC66; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                            ✅ بدء المسح
                        </button>
                        <button id="cancelPageCount" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                            ❌ إلغاء
                        </button>
                    </div>
                </div>
            `;

            return modal;
        }

        // حفظ الكتاب
        document.getElementById('bookForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // التحقق من البيانات المطلوبة
            const bookNumber = document.getElementById('bookNumber').value.trim();
            const bookDate = document.getElementById('bookDate').value;

            if (!bookNumber) {
                alert('رقم الكتاب مطلوب!');
                document.getElementById('bookNumber').focus();
                return;
            }

            if (!bookDate) {
                alert('تاريخ الكتاب مطلوب!');
                document.getElementById('bookDate').focus();
                return;
            }

            // جمع بيانات النموذج
            const formData = {
                bookNumber: bookNumber,
                bookDate: bookDate,
                bookSubject: document.getElementById('bookSubject').value,
                issuingAuthority: document.getElementById('issuingAuthority').value,
                executionDepartment: document.getElementById('executionDepartment').value,
                recipientAuthority: document.getElementById('recipientAuthority').value,
                fileNumber: document.getElementById('fileNumber').value,
                details: document.getElementById('details').value,
                images: uploadedImages
            };

            // محاكاة حفظ البيانات
            saveBook(formData);
        });

        // معالجة الصور الممسوحة
        async function processScannedImages(scannedPages) {
            const subject = document.getElementById('bookSubject').value || 'مستند_ممسوح';

            // إضافة جميع الصور للمعاينة أولاً
            for (let i = 0; i < scannedPages.length; i++) {
                const page = scannedPages[i];

                // تحديد اسم الملف للعرض
                let displayName;
                if (scannedPages.length === 1) {
                    displayName = subject;
                } else {
                    displayName = subject + '_صفحة_' + String(i + 1).padStart(2, '0');
                }

                // إضافة الصورة للمعاينة
                addScannedImageToPreview(page, displayName);
            }

            // حفظ الصور كـ PDF
            try {
                const outputDir = await getOutputDirectory();

                if (scannedPages.length === 1) {
                    // صفحة واحدة - حفظ كملف PDF منفصل
                    const pdf = await pdfGenerator.createSinglePagePDF(scannedPages[0], subject, {
                        orientation: 'portrait',
                        format: 'a4',
                        margin: 10
                    });

                    const savedPath = await pdfGenerator.savePDF(pdf, subject, outputDir);
                    console.log('تم حفظ PDF في:', savedPath);

                } else {
                    // عدة صفحات - دمج في ملف PDF واحد
                    const multiPagePDF = await pdfGenerator.createMultiPagePDF(scannedPages, subject, {
                        orientation: 'portrait',
                        format: 'a4',
                        margin: 10
                    });

                    const savedPath = await pdfGenerator.savePDF(multiPagePDF, subject, outputDir);
                    console.log('تم حفظ PDF متعدد الصفحات في:', savedPath);

                    // حفظ كل صفحة منفصلة أيضاً (اختياري)
                    const saveIndividualPages = await askUserForIndividualPages();
                    if (saveIndividualPages) {
                        for (let i = 0; i < scannedPages.length; i++) {
                            const pageFileName = subject + '_صفحة_' + String(i + 1).padStart(2, '0');
                            const pagePDF = await pdfGenerator.createSinglePagePDF(scannedPages[i], pageFileName, {
                                orientation: 'portrait',
                                format: 'a4',
                                margin: 10
                            });
                            await pdfGenerator.savePDF(pagePDF, pageFileName, outputDir);
                        }
                    }
                }

            } catch (error) {
                console.error('خطأ في حفظ PDF:', error);
                showErrorMessage('حدث خطأ أثناء حفظ ملفات PDF: ' + error.message);
            }
        }

        // سؤال المستخدم عن حفظ الصفحات منفصلة
        async function askUserForIndividualPages() {
            return new Promise((resolve) => {
                const result = confirm('هل تريد حفظ كل صفحة كملف PDF منفصل أيضاً؟\n\n• موافق: حفظ ملف مدمج + ملفات منفصلة\n• إلغاء: حفظ الملف المدمج فقط');
                resolve(result);
            });
        }

        // إضافة الصورة الممسوحة للمعاينة
        function addScannedImageToPreview(imageData, fileName) {
            imageCounter++;
            const imageId = 'img_' + imageCounter;

            const imageItem = document.createElement('div');
            imageItem.className = 'preview-item';
            imageItem.id = imageId;

            imageItem.innerHTML = `
                <img src="${imageData.data || imageData.imageData}" alt="صورة ممسوحة" class="preview-image">
                <div class="preview-name">${fileName}</div>
                <button type="button" class="remove-image" onclick="removeImage('${imageId}')">×</button>
            `;

            document.getElementById('imagePreview').appendChild(imageItem);

            uploadedImages.push({
                id: imageId,
                file: null, // الصورة الممسوحة ليس لها ملف
                name: fileName,
                data: imageData.data || imageData.imageData,
                scanned: true
            });
        }

        // الحصول على مجلد الحفظ
        async function getOutputDirectory() {
            try {
                // قراءة الإعدادات للحصول على مجلد الحفظ
                const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');
                return settings.scanSavePath || path.join(os.homedir(), 'Documents', 'BookArchiveScans');
            } catch (error) {
                return path.join(os.homedir(), 'Documents', 'BookArchiveScans');
            }
        }

        // إظهار رسالة المسح
        function showScanningMessage(message) {
            const scanningDiv = document.createElement('div');
            scanningDiv.id = 'scanningMessage';
            scanningDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                text-align: center;
                min-width: 300px;
            `;

            scanningDiv.innerHTML = `
                <div style="color: #00CC66; font-size: 48px; margin-bottom: 15px;">🖨️</div>
                <h3 style="color: #00CC66; margin-bottom: 15px;">المسح الضوئي</h3>
                <p id="scanningText" style="margin-bottom: 20px;">${message}</p>
                <div id="scanProgressBar" style="width: 100%; height: 10px; background: #f0f0f0; border-radius: 5px; overflow: hidden;">
                    <div style="width: 0%; height: 100%; background: #00CC66; transition: width 0.3s ease;"></div>
                </div>
                <p id="scanProgressText" style="margin-top: 10px; font-size: 12px; color: #666;"></p>
            `;

            document.body.appendChild(scanningDiv);
        }

        // تحديث رسالة المسح
        function updateScanningMessage(message) {
            const scanningText = document.getElementById('scanningText');
            if (scanningText) {
                scanningText.textContent = message;
            }
        }

        // إخفاء رسالة المسح
        function hideScanningMessage() {
            const scanningMessage = document.getElementById('scanningMessage');
            if (scanningMessage) {
                document.body.removeChild(scanningMessage);
            }
        }

        // عرض معلومات الماسحات الضوئية
        async function showScannerInfo() {
            try {
                showScanningMessage('جاري البحث عن الماسحات الضوئية...');

                const scanners = await scanner.getScannerList();

                hideScanningMessage();

                if (scanners.length === 0) {
                    showErrorMessage('لم يتم العثور على أي ماسح ضوئي متصل.\n\nتأكد من:\n• توصيل الماسح الضوئي بالكمبيوتر\n• تثبيت التعريفات المناسبة\n• تشغيل الماسح الضوئي');
                    return;
                }

                // إنشاء نافذة معلومات الماسحات
                const infoModal = createScannerInfoModal(scanners);
                document.body.appendChild(infoModal);

            } catch (error) {
                hideScanningMessage();
                console.error('خطأ في الحصول على معلومات الماسحات:', error);
                showErrorMessage('حدث خطأ أثناء البحث عن الماسحات الضوئية: ' + error.message);
            }
        }

        // إنشاء نافذة معلومات الماسحات
        function createScannerInfoModal(scanners) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const scannersHtml = scanners.map((scanner, index) => `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #00CC66;">
                    <h4 style="margin: 0 0 10px 0; color: #00CC66;">📱 ماسح ضوئي ${index + 1}</h4>
                    <p style="margin: 5px 0;"><strong>الاسم:</strong> ${scanner.name || 'غير محدد'}</p>
                    <p style="margin: 5px 0;"><strong>الحالة:</strong>
                        <span style="color: ${scanner.available ? '#00CC66' : '#FF3333'};">
                            ${scanner.available ? '✅ متاح' : '❌ غير متاح'}
                        </span>
                    </p>
                    <p style="margin: 5px 0; font-size: 12px; color: #666;"><strong>معرف الجهاز:</strong> ${scanner.deviceId || 'غير محدد'}</p>
                </div>
            `).join('');

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #00CC66; margin-bottom: 20px; text-align: center;">🖨️ الماسحات الضوئية المتاحة</h3>
                    <div style="margin-bottom: 20px;">
                        ${scannersHtml}
                    </div>
                    <div style="text-align: center;">
                        <button onclick="this.closest('.modal').remove()" style="background: #00CC66; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            ✅ موافق
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'modal';

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            return modal;
        }

        // فتح مجلد الحفظ
        async function openSaveFolder() {
            try {
                const outputDir = await getOutputDirectory();

                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('open-save-folder', outputDir);
                    if (result.success) {
                        showSuccessMessage('تم فتح مجلد الحفظ بنجاح');
                    } else {
                        showErrorMessage('فشل في فتح مجلد الحفظ: ' + result.error);
                    }
                } else {
                    // في المتصفح، عرض المسار فقط
                    alert('مجلد الحفظ:\n' + outputDir);
                }
            } catch (error) {
                console.error('خطأ في فتح مجلد الحفظ:', error);
                showErrorMessage('حدث خطأ أثناء فتح مجلد الحفظ: ' + error.message);
            }
        }

        // تشغيل اختبار النظام
        async function runScannerTest() {
            try {
                showScanningMessage('جاري تشغيل اختبار النظام...');

                // تشغيل الاختبار السريع أولاً
                const quickResult = await scannerTest.quickScannerTest();

                hideScanningMessage();

                if (quickResult.error) {
                    showErrorMessage('فشل الاختبار السريع: ' + quickResult.error);
                    return;
                }

                // عرض نتائج الاختبار السريع
                const quickTestModal = createQuickTestModal(quickResult);
                document.body.appendChild(quickTestModal);

            } catch (error) {
                hideScanningMessage();
                console.error('خطأ في اختبار النظام:', error);
                showErrorMessage('حدث خطأ أثناء اختبار النظام: ' + error.message);
            }
        }

        // إنشاء نافذة نتائج الاختبار السريع
        function createQuickTestModal(result) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const scannerStatus = result.scannerAvailable ? '✅ متوفر' : '❌ غير متوفر';
            const electronStatus = result.electronAPI ? '✅ يعمل' : '❌ لا يعمل';
            const pdfStatus = result.pdfGenerator ? '✅ متوفر' : '❌ غير متوفر';

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px;">
                    <h3 style="color: #00CC66; margin-bottom: 20px; text-align: center;">🧪 نتائج اختبار النظام</h3>

                    <div style="margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4 style="margin: 0 0 10px 0; color: #333;">🖨️ حالة الماسح الضوئي</h4>
                            <p style="margin: 5px 0;"><strong>التوفر:</strong> ${scannerStatus}</p>
                            <p style="margin: 5px 0;"><strong>عدد الماسحات:</strong> ${result.scannersCount}</p>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4 style="margin: 0 0 10px 0; color: #333;">⚡ النظام</h4>
                            <p style="margin: 5px 0;"><strong>Electron API:</strong> ${electronStatus}</p>
                            <p style="margin: 5px 0;"><strong>مولد PDF:</strong> ${pdfStatus}</p>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="runFullTest()" style="background: #00CC66; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                            🔍 اختبار شامل
                        </button>
                        <button onclick="this.closest('div').remove()" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                            ✅ إغلاق
                        </button>
                    </div>
                </div>
            `;

            // إضافة وظيفة الاختبار الشامل
            window.runFullTest = async () => {
                modal.remove();
                showScanningMessage('جاري تشغيل الاختبار الشامل...');

                try {
                    await scannerTest.runAllTests();
                    hideScanningMessage();
                } catch (error) {
                    hideScanningMessage();
                    showErrorMessage('فشل الاختبار الشامل: ' + error.message);
                }
            };

            return modal;
        }

        // حفظ الكتاب في قاعدة البيانات
        function saveBook(data) {
            // إظهار رسالة التحميل
            const saveBtn = document.querySelector('.save-btn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '⏳ جاري الحفظ...';
            saveBtn.disabled = true;

            // حفظ البيانات في قاعدة البيانات المحلية
            setTimeout(async () => {
                try {
                    await booksDB.saveBook(data);

                    // إظهار رسالة نجاح تختفي تلقائياً
                    showSuccessMessage('تم حفظ البيانات بنجاح في قاعدة البيانات');

                    // إعادة تعيين النموذج بدون تأكيد (بعد الحفظ الناجح)
                    resetFormAfterSave();

                    // تحديث رقم الكتاب التالي
                    loadNextBookId();

                } catch (error) {
                    console.error('خطأ في حفظ البيانات:', error);
                    showErrorMessage('حدث خطأ أثناء حفظ البيانات');
                }

                // إعادة تفعيل الزر
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;

            }, 1000);
        }

        // مسح النموذج (مع طلب تأكيد)
        function clearForm() {
            if (confirm('هل تريد مسح جميع البيانات المدخلة؟')) {
                document.getElementById('bookForm').reset();
                document.getElementById('imagePreview').innerHTML = '';
                uploadedImages = [];
                imageCounter = 0;
            }
        }

        // إعادة تعيين النموذج بعد الحفظ الناجح (بدون طلب تأكيد)
        function resetFormAfterSave() {
            document.getElementById('bookForm').reset();
            document.getElementById('imagePreview').innerHTML = '';
            uploadedImages = [];
            imageCounter = 0;

            // تعيين التاريخ الحالي مرة أخرى
            document.getElementById('bookDate').value = new Date().toISOString().split('T')[0];
        }

        // العودة للوحة المعلومات
        function goBack() {
            if (uploadedImages.length > 0 || document.getElementById('bookNumber').value.trim()) {
                if (confirm('هناك بيانات غير محفوظة. هل تريد المتابعة؟')) {
                    window.location.href = 'MainDashboard.html';
                }
            } else {
                window.location.href = 'MainDashboard.html';
            }
        }

        // إظهار المساعدة
        function showHelp() {
            const helpModal = createHelpModal();
            document.body.appendChild(helpModal);
        }

        // إنشاء نافذة المساعدة
        function createHelpModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 700px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #00CC66; margin-bottom: 20px; text-align: center;">❓ مساعدة - إدخال بيانات كتاب جديد</h3>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #FF3333; margin-bottom: 10px;">📋 الحقول المطلوبة:</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            <li><strong>رقم الكتاب:</strong> مطلوب ولا يمكن الحفظ بدونه</li>
                            <li><strong>تاريخ الكتاب:</strong> مطلوب ولا يمكن الحفظ بدونه</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #0066CC; margin-bottom: 10px;">📝 الحقول الاختيارية:</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            <li>موضوع الكتاب</li>
                            <li>الجهة الصادر منها</li>
                            <li>جهة التنفيذ</li>
                            <li>الجهة المرسل إليها</li>
                            <li>رقم الإضبارة</li>
                            <li>التفاصيل</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #00CC66; margin-bottom: 10px;">📷 إضافة الصور:</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            <li><strong>اختيار من الكمبيوتر:</strong> تحديد ملفات الصور المحفوظة</li>
                            <li><strong>المسح الضوئي:</strong> مسح الوثائق مباشرة من الماسح الضوئي</li>
                            <li><strong>معلومات الماسح:</strong> عرض الماسحات المتصلة وحالتها</li>
                            <li><strong>فتح مجلد الحفظ:</strong> الوصول لمجلد حفظ الملفات الممسوحة</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #FF9900; margin-bottom: 10px;">🖨️ ميزات المسح الضوئي:</h4>
                        <ul style="margin: 0; padding-right: 20px;">
                            <li>دعم المسح المتعدد الصفحات</li>
                            <li>حفظ تلقائي كملفات PDF</li>
                            <li>تسمية تلقائية حسب موضوع الكتاب</li>
                            <li>دمج الصفحات المتعددة في ملف واحد</li>
                            <li>إضافة تاريخ المسح وأرقام الصفحات</li>
                        </ul>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="color: #666; margin-bottom: 10px;">💡 نصائح:</h4>
                        <ul style="margin: 0; padding-right: 20px; font-size: 14px;">
                            <li>تأكد من تشغيل الماسح الضوئي قبل بدء المسح</li>
                            <li>اكتب موضوع الكتاب قبل المسح لتسمية أفضل للملفات</li>
                            <li>يمكن مسح عدة صفحات في عملية واحدة</li>
                            <li>الصور الممسوحة تحفظ في مجلد المستندات/BookArchiveScans</li>
                        </ul>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="this.closest('.help-modal').remove()" style="background: #00CC66; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            ✅ فهمت
                        </button>
                    </div>
                </div>
            `;

            modal.className = 'help-modal';

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            return modal;
        }

        // إظهار رسالة خطأ
        function showErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #FF3333;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(255,51,51,0.3);
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 5000);
        }

        // إظهار رسالة نجاح تختفي تلقائياً
        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #00CC66;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,204,102,0.3);
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            // إضافة الأنيميشن
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // تحميل رقم الكتاب التالي من قاعدة البيانات
        async function loadNextBookId() {
            try {
                const stats = await booksDB.getStatistics();
                const nextId = stats.totalBooks + 1;
                document.getElementById('nextBookId').textContent = nextId.toString();
            } catch (error) {
                console.error('خطأ في تحميل رقم الكتاب التالي:', error);
                document.getElementById('nextBookId').textContent = '1';
            }
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadNextBookId();
            // تعيين التاريخ الحالي كافتراضي
            document.getElementById('bookDate').value = new Date().toISOString().split('T')[0];

            // Enter Key Navigation for form inputs
            const bookFormElement = document.getElementById('bookForm');
            if (bookFormElement) {
                const focusableInputs = Array.from(
                    bookFormElement.querySelectorAll('input[type="text"], input[type="date"], textarea')
                ).filter(el => {
                    const style = window.getComputedStyle(el);
                    // Ensure element is visible, not disabled, and part of tab order
                    return style.display !== 'none' && style.visibility !== 'hidden' && !el.disabled && el.tabIndex !== -1;
                });

                focusableInputs.forEach((inputEl, currentIndex) => {
                    inputEl.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault(); // Prevent default form submission or line break in textarea
                            
                            let nextFocusIndex = (currentIndex + 1);
                            // Loop to find the next truly focusable element
                            for (let i = 0; i < focusableInputs.length; i++) {
                                const potentialNextElement = focusableInputs[nextFocusIndex % focusableInputs.length];
                                const potentialNextStyle = window.getComputedStyle(potentialNextElement);
                                if (potentialNextStyle.display !== 'none' &&
                                    potentialNextStyle.visibility !== 'hidden' &&
                                    !potentialNextElement.disabled &&
                                    potentialNextElement.tabIndex !== -1) {
                                    potentialNextElement.focus();
                                    break; // Exit loop once focus is set
                                }
                                nextFocusIndex++; // Try next element in the list
                            }
                        }
                    });
                });
            }
        });

        // تحديث أسماء الصور عند تغيير الموضوع
        document.getElementById('bookSubject').addEventListener('input', function() {
            const newSubject = this.value || 'صورة';
            uploadedImages.forEach((img, index) => {
                let newName;
                if (img.scanned) {
                    // للصور الممسوحة، استخدم تنسيق مختلف
                    newName = uploadedImages.filter(i => i.scanned).length === 1 ?
                        newSubject :
                        newSubject + '_' + String(uploadedImages.filter((i, idx) => i.scanned && idx <= index).length).padStart(3, '0');
                } else {
                    // للصور العادية
                    newName = index === 0 ? newSubject : newSubject + '_' + String(index).padStart(2, '0');
                }

                img.name = newName;
                const nameElement = document.querySelector(`#${img.id} .preview-name`);
                if (nameElement) {
                    nameElement.textContent = newName;
                }
            });
        });

        // إضافة مستمع لتقدم المسح
        document.addEventListener('scanProgress', (event) => {
            const { current, total, percentage, message } = event.detail;

            // تحديث شريط التقدم
            const progressBar = document.querySelector('#scanningMessage #scanProgressBar div');
            const progressText = document.getElementById('scanProgressText');

            if (progressBar) {
                progressBar.style.width = percentage + '%';
            }

            if (progressText) {
                progressText.textContent = message;
            }

            // تحديث النص الرئيسي
            updateScanningMessage(`جاري مسح الصفحة ${current} من ${total}...`);
        });
    </script>
</body>
</html>
