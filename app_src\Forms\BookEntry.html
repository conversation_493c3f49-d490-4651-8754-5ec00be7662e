<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدخال بيانات كتاب جديد - برنامج الأرشفة الإلكترونية</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 50%, #CCE7FF 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .header-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .navigation {
            background: white;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 102, 204, 0.4);
        }

        .form-container {
            max-width: 1000px;
            margin: 30px auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-title {
            font-size: 24px;
            font-weight: bold;
            color: #00CC66;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .required {
            color: #FF3333;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #00CC66;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #00FF80;
            box-shadow: 0 0 10px rgba(0, 204, 102, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .image-upload-section {
            background: #F8F9FA;
            border: 2px dashed #00CC66;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .image-upload-section:hover {
            border-color: #00FF80;
            background: #F0FFF0;
        }

        .upload-icon {
            font-size: 48px;
            color: #00CC66;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }

        .upload-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .upload-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 204, 102, 0.4);
        }

        .scanner-btn {
            background: linear-gradient(45deg, #FF9900, #FFAA33);
        }

        .scanner-btn:hover {
            box-shadow: 0 6px 20px rgba(255, 153, 0, 0.4);
        }

        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .preview-item {
            position: relative;
            border: 2px solid #00CC66;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .preview-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .preview-name {
            padding: 8px;
            font-size: 12px;
            background: #F8F9FA;
            text-align: center;
        }

        .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #FF3333;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #E9ECEF;
        }

        .save-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 204, 102, 0.4);
        }

        .save-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 204, 102, 0.6);
        }

        .clear-btn {
            background: linear-gradient(45deg, #666666, #888888);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                margin: 20px;
                padding: 20px;
            }
            
            .upload-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .form-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1 class="header-title">إدخال بيانات كتاب جديد</h1>
        <p>إضافة كتاب جديد إلى الأرشيف الإلكتروني</p>
    </header>

    <nav class="navigation">
        <button class="nav-button" onclick="goBack()">🔙 العودة للوحة المعلومات</button>
        <span>رقم الكتاب التالي: <strong id="nextBookId">1</strong></span>
        <button class="nav-button" onclick="showHelp()">❓ المساعدة</button>
    </nav>

    <div class="form-container">
        <h2 class="form-title">📝 نموذج إدخال بيانات الكتاب</h2>

        <form id="bookForm">
            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label" for="bookNumber">
                        رقم الكتاب <span class="required">*</span>
                    </label>
                    <input type="text" id="bookNumber" name="bookNumber" class="form-input" required>
                    <div class="help-text">رقم الكتاب مطلوب ولا يمكن الحفظ بدونه</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="bookDate">
                        تاريخ الكتاب <span class="required">*</span>
                    </label>
                    <input type="date" id="bookDate" name="bookDate" class="form-input" required placeholder="dd/mm/yyyy">
                    <div class="help-text">تاريخ الكتاب مطلوب ولا يمكن الحفظ بدونه</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="bookSubject">
                        موضوع الكتاب
                    </label>
                    <input type="text" id="bookSubject" name="bookSubject" class="form-input">
                    <div class="help-text">موضوع الكتاب (اختياري)</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="issuingAuthority">
                        الجهة الصادر منها الكتاب
                    </label>
                    <input type="text" id="issuingAuthority" name="issuingAuthority" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="executionDepartment">
                        جهة التنفيذ
                    </label>
                    <input type="text" id="executionDepartment" name="executionDepartment" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="recipientAuthority">
                        الجهة المرسل إليها الكتاب
                    </label>
                    <input type="text" id="recipientAuthority" name="recipientAuthority" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label" for="fileNumber">
                        رقم إضبارة الحفظ
                    </label>
                    <input type="text" id="fileNumber" name="fileNumber" class="form-input">
                </div>

                <div class="form-group full-width">
                    <label class="form-label" for="details">
                        التفاصيل
                    </label>
                    <textarea id="details" name="details" class="form-input form-textarea" rows="4"></textarea>
                    <div class="help-text">تفاصيل إضافية عن الكتاب</div>
                </div>
            </div>

            <div class="form-group full-width">
                <label class="form-label">صور الكتاب</label>
                <div class="image-upload-section">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">إضافة صور للكتاب</div>
                    <div class="upload-buttons">
                        <button type="button" class="upload-btn" onclick="selectFiles()">
                            📁 اختيار من الكمبيوتر
                        </button>
                        <button type="button" class="upload-btn scanner-btn" onclick="scanDocument()">
                            🖨️ مسح ضوئي
                        </button>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    <div class="help-text">يمكن إضافة عدة صور للكتاب الواحد. الصورة الأولى ستحفظ باسم الموضوع، والثانية باسم الموضوع + 01</div>
                </div>

                <div id="imagePreview" class="image-preview"></div>
            </div>

            <div class="form-actions">
                <button type="submit" class="save-btn">
                    💾 حفظ الكتاب
                </button>
                <button type="button" class="clear-btn" onclick="clearForm()">
                    🗑️ مسح النموذج
                </button>
            </div>
        </form>
    </div>

    <script src="../Database/books_database.js"></script>
    <script>
        let uploadedImages = [];
        let imageCounter = 0;

        // تم نقل وظيفة loadNextBookId إلى الأسفل مع دعم قاعدة البيانات

        // اختيار الملفات
        function selectFiles() {
            document.getElementById('fileInput').click();
        }

        // التعامل مع اختيار الملفات
        function handleFileSelect(event) {
            const files = event.target.files;
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    addImageToPreview(file);
                }
            }
        }

        // إضافة صورة للمعاينة
        function addImageToPreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imageCounter++;
                const imageId = 'img_' + imageCounter;

                const imageItem = document.createElement('div');
                imageItem.className = 'preview-item';
                imageItem.id = imageId;

                // تحديد اسم الصورة بناءً على الموضوع
                const subject = document.getElementById('bookSubject').value || 'صورة';
                const imageName = uploadedImages.length === 0 ? subject : subject + '_' + String(uploadedImages.length).padStart(2, '0');

                imageItem.innerHTML = `
                    <img src="${e.target.result}" alt="صورة الكتاب" class="preview-image">
                    <div class="preview-name">${imageName}</div>
                    <button type="button" class="remove-image" onclick="removeImage('${imageId}')">×</button>
                `;

                document.getElementById('imagePreview').appendChild(imageItem);

                uploadedImages.push({
                    id: imageId,
                    file: file,
                    name: imageName,
                    data: e.target.result
                });
            };
            reader.readAsDataURL(file);
        }

        // إزالة صورة
        function removeImage(imageId) {
            const imageElement = document.getElementById(imageId);
            if (imageElement) {
                imageElement.remove();
                uploadedImages = uploadedImages.filter(img => img.id !== imageId);
            }
        }

        // مسح ضوئي
        function scanDocument() {
            // هنا يتم تفعيل الماسح الضوئي
            alert('سيتم تفعيل الماسح الضوئي...\n\nملاحظة: تأكد من تشغيل الماسح الضوئي وتثبيت التعريفات المناسبة.');

            // محاكاة عملية المسح
            setTimeout(() => {
                const confirmed = confirm('هل تم المسح بنجاح؟ اضغط موافق لإضافة الصورة الممسوحة.');
                if (confirmed) {
                    // هنا يتم إضافة الصورة الممسوحة
                    // مؤقتاً سنعرض رسالة
                    alert('تم إضافة الصورة الممسوحة بنجاح!');
                }
            }, 2000);
        }

        // حفظ الكتاب
        document.getElementById('bookForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // التحقق من البيانات المطلوبة
            const bookNumber = document.getElementById('bookNumber').value.trim();
            const bookDate = document.getElementById('bookDate').value;

            if (!bookNumber) {
                alert('رقم الكتاب مطلوب!');
                document.getElementById('bookNumber').focus();
                return;
            }

            if (!bookDate) {
                alert('تاريخ الكتاب مطلوب!');
                document.getElementById('bookDate').focus();
                return;
            }

            // جمع بيانات النموذج
            const formData = {
                bookNumber: bookNumber,
                bookDate: bookDate,
                bookSubject: document.getElementById('bookSubject').value,
                issuingAuthority: document.getElementById('issuingAuthority').value,
                executionDepartment: document.getElementById('executionDepartment').value,
                recipientAuthority: document.getElementById('recipientAuthority').value,
                fileNumber: document.getElementById('fileNumber').value,
                details: document.getElementById('details').value,
                images: uploadedImages
            };

            // محاكاة حفظ البيانات
            saveBook(formData);
        });

        // حفظ الكتاب في قاعدة البيانات
        function saveBook(data) {
            // إظهار رسالة التحميل
            const saveBtn = document.querySelector('.save-btn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '⏳ جاري الحفظ...';
            saveBtn.disabled = true;

            // حفظ البيانات في قاعدة البيانات المحلية
            setTimeout(async () => {
                try {
                    await booksDB.saveBook(data);

                    // إظهار رسالة نجاح تختفي تلقائياً
                    showSuccessMessage('تم حفظ البيانات بنجاح في قاعدة البيانات');

                    // إعادة تعيين النموذج بدون تأكيد (بعد الحفظ الناجح)
                    resetFormAfterSave();

                    // تحديث رقم الكتاب التالي
                    loadNextBookId();

                } catch (error) {
                    console.error('خطأ في حفظ البيانات:', error);
                    showErrorMessage('حدث خطأ أثناء حفظ البيانات');
                }

                // إعادة تفعيل الزر
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;

            }, 1000);
        }

        // مسح النموذج (مع طلب تأكيد)
        function clearForm() {
            if (confirm('هل تريد مسح جميع البيانات المدخلة؟')) {
                document.getElementById('bookForm').reset();
                document.getElementById('imagePreview').innerHTML = '';
                uploadedImages = [];
                imageCounter = 0;
            }
        }

        // إعادة تعيين النموذج بعد الحفظ الناجح (بدون طلب تأكيد)
        function resetFormAfterSave() {
            document.getElementById('bookForm').reset();
            document.getElementById('imagePreview').innerHTML = '';
            uploadedImages = [];
            imageCounter = 0;

            // تعيين التاريخ الحالي مرة أخرى
            document.getElementById('bookDate').value = new Date().toISOString().split('T')[0];
        }

        // العودة للوحة المعلومات
        function goBack() {
            if (uploadedImages.length > 0 || document.getElementById('bookNumber').value.trim()) {
                if (confirm('هناك بيانات غير محفوظة. هل تريد المتابعة؟')) {
                    window.location.href = 'MainDashboard.html';
                }
            } else {
                window.location.href = 'MainDashboard.html';
            }
        }

        // إظهار المساعدة
        function showHelp() {
            alert(`مساعدة - إدخال بيانات كتاب جديد

الحقول المطلوبة:
• رقم الكتاب: مطلوب ولا يمكن الحفظ بدونه
• تاريخ الكتاب: مطلوب ولا يمكن الحفظ بدونه

الحقول الاختيارية:
• موضوع الكتاب
• الجهة الصادر منها
• جهة التنفيذ
• الجهة المرسل إليها
• رقم الإضبارة
• التفاصيل

الصور:
• يمكن إضافة عدة صور للكتاب الواحد
• الصورة الأولى تحفظ باسم الموضوع
• الصور التالية تحفظ باسم الموضوع + رقم متسلسل
• يمكن استخدام الماسح الضوئي أو اختيار من الكمبيوتر`);
        }

        // إظهار رسالة خطأ
        function showErrorMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #FF3333;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(255,51,51,0.3);
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 5000);
        }

        // إظهار رسالة نجاح تختفي تلقائياً
        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #00CC66;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,204,102,0.3);
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            // إضافة الأنيميشن
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // تحميل رقم الكتاب التالي من قاعدة البيانات
        async function loadNextBookId() {
            try {
                const stats = await booksDB.getStatistics();
                const nextId = stats.totalBooks + 1;
                document.getElementById('nextBookId').textContent = nextId.toString();
            } catch (error) {
                console.error('خطأ في تحميل رقم الكتاب التالي:', error);
                document.getElementById('nextBookId').textContent = '1';
            }
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadNextBookId();
            // تعيين التاريخ الحالي كافتراضي
            document.getElementById('bookDate').value = new Date().toISOString().split('T')[0];

            // Enter Key Navigation for form inputs
            const bookFormElement = document.getElementById('bookForm');
            if (bookFormElement) {
                const focusableInputs = Array.from(
                    bookFormElement.querySelectorAll('input[type="text"], input[type="date"], textarea')
                ).filter(el => {
                    const style = window.getComputedStyle(el);
                    // Ensure element is visible, not disabled, and part of tab order
                    return style.display !== 'none' && style.visibility !== 'hidden' && !el.disabled && el.tabIndex !== -1;
                });

                focusableInputs.forEach((inputEl, currentIndex) => {
                    inputEl.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault(); // Prevent default form submission or line break in textarea
                            
                            let nextFocusIndex = (currentIndex + 1);
                            // Loop to find the next truly focusable element
                            for (let i = 0; i < focusableInputs.length; i++) {
                                const potentialNextElement = focusableInputs[nextFocusIndex % focusableInputs.length];
                                const potentialNextStyle = window.getComputedStyle(potentialNextElement);
                                if (potentialNextStyle.display !== 'none' &&
                                    potentialNextStyle.visibility !== 'hidden' &&
                                    !potentialNextElement.disabled &&
                                    potentialNextElement.tabIndex !== -1) {
                                    potentialNextElement.focus();
                                    break; // Exit loop once focus is set
                                }
                                nextFocusIndex++; // Try next element in the list
                            }
                        }
                    });
                });
            }
        });

        // تحديث أسماء الصور عند تغيير الموضوع
        document.getElementById('bookSubject').addEventListener('input', function() {
            const newSubject = this.value || 'صورة';
            uploadedImages.forEach((img, index) => {
                const newName = index === 0 ? newSubject : newSubject + '_' + String(index).padStart(2, '0');
                img.name = newName;
                const nameElement = document.querySelector(`#${img.id} .preview-name`);
                if (nameElement) {
                    nameElement.textContent = newName;
                }
            });
        });
    </script>
</body>
</html>
