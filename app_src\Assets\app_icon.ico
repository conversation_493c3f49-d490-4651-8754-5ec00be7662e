# This is a placeholder for the application icon
# In a real implementation, this would be a binary ICO file
# For now, we'll create an HTML-based icon that can be converted to ICO

<!DOCTYPE html>
<html>
<head>
    <style>
        .icon-container {
            width: 64px;
            height: 64px;
            background: linear-gradient(45deg, #FF3333, #FF6666);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(255,51,51,0.3);
            position: relative;
            overflow: hidden;
        }
        
        .icon-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
        }
        
        .archive-icon {
            font-size: 32px;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            z-index: 1;
        }
        
        .file-stack {
            position: absolute;
            width: 40px;
            height: 40px;
        }
        
        .file {
            position: absolute;
            width: 24px;
            height: 30px;
            background: white;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .file:nth-child(1) {
            top: 8px;
            left: 8px;
            transform: rotate(-5deg);
            opacity: 0.8;
        }
        
        .file:nth-child(2) {
            top: 12px;
            left: 12px;
            transform: rotate(0deg);
            opacity: 0.9;
        }
        
        .file:nth-child(3) {
            top: 16px;
            left: 16px;
            transform: rotate(5deg);
            opacity: 1;
        }
        
        .file::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 3px;
            right: 3px;
            height: 2px;
            background: #FF3333;
            border-radius: 1px;
        }
        
        .file::after {
            content: '';
            position: absolute;
            top: 8px;
            left: 3px;
            right: 6px;
            height: 1px;
            background: #ccc;
            border-radius: 0.5px;
        }
    </style>
</head>
<body>
    <div class="icon-container">
        <div class="file-stack">
            <div class="file"></div>
            <div class="file"></div>
            <div class="file"></div>
        </div>
    </div>
</body>
</html>
