// إعدادات المسح الضوئي
class ScannerSettings {
    constructor() {
        this.defaultSettings = {
            resolution: 300,
            colorMode: 'color', // color, grayscale, blackwhite
            format: 'png',
            quality: 90,
            autoSave: true,
            savePath: '',
            multiPageMode: 'combined', // combined, separate, both
            pdfSettings: {
                orientation: 'portrait',
                format: 'a4',
                margin: 10,
                addTimestamp: true,
                addPageNumbers: true
            },
            scannerPreferences: {
                preferredScanner: '',
                autoDetect: true,
                timeout: 30000
            }
        };
        
        this.currentSettings = { ...this.defaultSettings };
        this.loadSettings();
    }

    // تحميل الإعدادات
    async loadSettings() {
        try {
            // محاولة تحميل من localStorage أولاً
            const localSettings = localStorage.getItem('scannerSettings');
            if (localSettings) {
                const parsed = JSON.parse(localSettings);
                this.currentSettings = { ...this.defaultSettings, ...parsed };
            }

            // تحميل من Electron إذا كان متوفراً
            if (window.electronAPI) {
                try {
                    const electronSettings = await window.electronAPI.invoke('load-scanner-settings');
                    if (electronSettings.success) {
                        this.currentSettings = { ...this.currentSettings, ...electronSettings.data };
                    }
                } catch (error) {
                    console.warn('فشل في تحميل إعدادات المسح من Electron:', error);
                }
            }

            // تطبيق الإعدادات على الماسح
            this.applyToScanner();
            
        } catch (error) {
            console.error('خطأ في تحميل إعدادات المسح:', error);
            this.currentSettings = { ...this.defaultSettings };
        }
    }

    // حفظ الإعدادات
    async saveSettings() {
        try {
            // حفظ في localStorage
            localStorage.setItem('scannerSettings', JSON.stringify(this.currentSettings));

            // حفظ في Electron إذا كان متوفراً
            if (window.electronAPI) {
                try {
                    await window.electronAPI.invoke('save-scanner-settings', this.currentSettings);
                } catch (error) {
                    console.warn('فشل في حفظ إعدادات المسح في Electron:', error);
                }
            }

            // تطبيق الإعدادات على الماسح
            this.applyToScanner();
            
            return true;
        } catch (error) {
            console.error('خطأ في حفظ إعدادات المسح:', error);
            return false;
        }
    }

    // تطبيق الإعدادات على الماسح
    applyToScanner() {
        if (window.scanner) {
            window.scanner.updateSettings({
                resolution: this.currentSettings.resolution,
                colorMode: this.currentSettings.colorMode,
                format: this.currentSettings.format,
                quality: this.currentSettings.quality
            });
        }
    }

    // الحصول على إعداد محدد
    getSetting(key) {
        const keys = key.split('.');
        let value = this.currentSettings;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    // تعيين إعداد محدد
    setSetting(key, value) {
        const keys = key.split('.');
        let target = this.currentSettings;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in target) || typeof target[k] !== 'object') {
                target[k] = {};
            }
            target = target[k];
        }
        
        target[keys[keys.length - 1]] = value;
    }

    // إعادة تعيين إلى الإعدادات الافتراضية
    resetToDefaults() {
        this.currentSettings = { ...this.defaultSettings };
        this.applyToScanner();
    }

    // الحصول على جميع الإعدادات
    getAllSettings() {
        return { ...this.currentSettings };
    }

    // تحديث إعدادات متعددة
    updateSettings(newSettings) {
        this.currentSettings = { ...this.currentSettings, ...newSettings };
        this.applyToScanner();
    }

    // إنشاء واجهة إعدادات المسح
    createSettingsUI() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 15px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h3 style="color: #00CC66; margin-bottom: 20px; text-align: center;">⚙️ إعدادات المسح الضوئي</h3>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">📷 إعدادات الصورة</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>الدقة (DPI):</strong>
                            <select id="resolution" style="width: 100%; padding: 5px; margin-top: 5px;">
                                <option value="150">150 DPI - جودة منخفضة</option>
                                <option value="300">300 DPI - جودة عادية</option>
                                <option value="600">600 DPI - جودة عالية</option>
                                <option value="1200">1200 DPI - جودة فائقة</option>
                            </select>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>نمط الألوان:</strong>
                            <select id="colorMode" style="width: 100%; padding: 5px; margin-top: 5px;">
                                <option value="color">ملون</option>
                                <option value="grayscale">رمادي</option>
                                <option value="blackwhite">أبيض وأسود</option>
                            </select>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>جودة الضغط (%):</strong>
                            <input type="range" id="quality" min="50" max="100" style="width: 100%; margin-top: 5px;">
                            <span id="qualityValue">90%</span>
                        </label>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">📄 إعدادات PDF</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>اتجاه الصفحة:</strong>
                            <select id="pdfOrientation" style="width: 100%; padding: 5px; margin-top: 5px;">
                                <option value="portrait">عمودي</option>
                                <option value="landscape">أفقي</option>
                            </select>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>حجم الصفحة:</strong>
                            <select id="pdfFormat" style="width: 100%; padding: 5px; margin-top: 5px;">
                                <option value="a4">A4</option>
                                <option value="a3">A3</option>
                                <option value="letter">Letter</option>
                            </select>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="addTimestamp" style="margin-left: 5px;">
                            <strong>إضافة تاريخ المسح</strong>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="addPageNumbers" style="margin-left: 5px;">
                            <strong>إضافة أرقام الصفحات</strong>
                        </label>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">📁 إعدادات الحفظ</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <label style="display: block; margin-bottom: 10px;">
                            <strong>وضع الصفحات المتعددة:</strong>
                            <select id="multiPageMode" style="width: 100%; padding: 5px; margin-top: 5px;">
                                <option value="combined">دمج في ملف واحد</option>
                                <option value="separate">ملفات منفصلة</option>
                                <option value="both">كلا الخيارين</option>
                            </select>
                        </label>
                        
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="autoSave" style="margin-left: 5px;">
                            <strong>حفظ تلقائي</strong>
                        </label>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button onclick="saveSettingsFromUI()" style="background: #00CC66; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                        ✅ حفظ الإعدادات
                    </button>
                    <button onclick="resetSettingsToDefaults()" style="background: #FF9900; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                        🔄 إعادة تعيين
                    </button>
                    <button onclick="this.closest('div').remove()" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
                        ❌ إلغاء
                    </button>
                </div>
            </div>
        `;

        // تعبئة القيم الحالية
        this.populateSettingsUI(modal);

        // إضافة معالجات الأحداث
        this.addSettingsEventHandlers(modal);

        return modal;
    }

    // تعبئة واجهة الإعدادات بالقيم الحالية
    populateSettingsUI(modal) {
        modal.querySelector('#resolution').value = this.currentSettings.resolution;
        modal.querySelector('#colorMode').value = this.currentSettings.colorMode;
        modal.querySelector('#quality').value = this.currentSettings.quality;
        modal.querySelector('#qualityValue').textContent = this.currentSettings.quality + '%';
        modal.querySelector('#pdfOrientation').value = this.currentSettings.pdfSettings.orientation;
        modal.querySelector('#pdfFormat').value = this.currentSettings.pdfSettings.format;
        modal.querySelector('#addTimestamp').checked = this.currentSettings.pdfSettings.addTimestamp;
        modal.querySelector('#addPageNumbers').checked = this.currentSettings.pdfSettings.addPageNumbers;
        modal.querySelector('#multiPageMode').value = this.currentSettings.multiPageMode;
        modal.querySelector('#autoSave').checked = this.currentSettings.autoSave;
    }

    // إضافة معالجات الأحداث
    addSettingsEventHandlers(modal) {
        const qualitySlider = modal.querySelector('#quality');
        const qualityValue = modal.querySelector('#qualityValue');
        
        qualitySlider.addEventListener('input', () => {
            qualityValue.textContent = qualitySlider.value + '%';
        });

        // حفظ الإعدادات
        window.saveSettingsFromUI = () => {
            this.currentSettings.resolution = parseInt(modal.querySelector('#resolution').value);
            this.currentSettings.colorMode = modal.querySelector('#colorMode').value;
            this.currentSettings.quality = parseInt(modal.querySelector('#quality').value);
            this.currentSettings.pdfSettings.orientation = modal.querySelector('#pdfOrientation').value;
            this.currentSettings.pdfSettings.format = modal.querySelector('#pdfFormat').value;
            this.currentSettings.pdfSettings.addTimestamp = modal.querySelector('#addTimestamp').checked;
            this.currentSettings.pdfSettings.addPageNumbers = modal.querySelector('#addPageNumbers').checked;
            this.currentSettings.multiPageMode = modal.querySelector('#multiPageMode').value;
            this.currentSettings.autoSave = modal.querySelector('#autoSave').checked;

            this.saveSettings().then(success => {
                if (success) {
                    alert('تم حفظ الإعدادات بنجاح!');
                    modal.remove();
                } else {
                    alert('فشل في حفظ الإعدادات!');
                }
            });
        };

        // إعادة تعيين الإعدادات
        window.resetSettingsToDefaults = () => {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                this.resetToDefaults();
                this.populateSettingsUI(modal);
            }
        };
    }
}

// إنشاء مثيل من إعدادات المسح
const scannerSettings = new ScannerSettings();

// تصدير للاستخدام العام
window.scannerSettings = scannerSettings;
