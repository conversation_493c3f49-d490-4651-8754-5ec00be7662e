-- إنشاء قاعدة بيانات الأرشفة الإلكترونية للكتب
-- Electronic Books Archive Database Creation Script
-- المبرمج: علي عاجل خشان المحنة

-- جدول الكتب الرئيسي
CREATE TABLE Books (
    BookID AUTOINCREMENT PRIMARY KEY,
    BookNumber TEXT(50) NOT NULL,
    BookDate DATETIME NOT NULL,
    BookSubject TEXT(255),
    IssuingAuthority TEXT(255),
    ExecutionDepartment TEXT(255),
    Details MEMO,
    RecipientAuthority TEXT(255),
    FileNumber TEXT(50),
    CreatedDate DATETIME DEFAULT Now(),
    ModifiedDate DATETIME DEFAULT Now(),
    CreatedBy TEXT(100),
    ModifiedBy TEXT(100)
);

-- جدول صور الكتب
CREATE TABLE BookImages (
    ImageID AUTOINCREMENT PRIMARY KEY,
    BookID LONG,
    ImageName TEXT(255),
    ImagePath TEXT(500),
    ImageDescription TEXT(255),
    ImageOrder INTEGER DEFAULT 1,
    CreatedDate DATETIME DEFAULT Now(),
    FOREIGN KEY (BookID) REFERENCES Books(BookID)
);

-- جدول المستخدمين
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) UNIQUE NOT NULL,
    Password TEXT(255) NOT NULL,
    FullName TEXT(100),
    Department TEXT(100),
    UserRole TEXT(20) DEFAULT 'User',
    IsActive YESNO DEFAULT True,
    LastLogin DATETIME,
    CreatedDate DATETIME DEFAULT Now()
);

-- جدول إعدادات النظام
CREATE TABLE SystemSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(100) UNIQUE NOT NULL,
    SettingValue TEXT(500),
    SettingDescription TEXT(255),
    ModifiedDate DATETIME DEFAULT Now(),
    ModifiedBy TEXT(100)
);

-- جدول سجل العمليات
CREATE TABLE ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID LONG,
    ActivityType TEXT(50),
    ActivityDescription TEXT(500),
    TableName TEXT(100),
    RecordID LONG,
    ActivityDate DATETIME DEFAULT Now(),
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- إدراج البيانات الأساسية
INSERT INTO Users (Username, Password, FullName, Department, UserRole) 
VALUES ('admin', 'a1234a1234A@#1', 'علي عاجل خشان المحنة', 'قسم الحسابات - شعبة الرواتب', 'Admin');

INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES
('AppTitle', 'برنامج الأرشفة الإلكترونية للكتب', 'عنوان التطبيق'),
('AppVersion', '1.0.0', 'إصدار التطبيق'),
('DefaultTheme', 'Blue', 'الثيم الافتراضي'),
('BackupPath', 'C:\Backup\BooksArchive\', 'مسار النسخ الاحتياطي'),
('MaxImageSize', '5242880', 'الحد الأقصى لحجم الصورة بالبايت (5MB)'),
('ScannerEnabled', 'True', 'تفعيل الماسح الضوئي'),
('AutoBackup', 'True', 'النسخ الاحتياطي التلقائي'),
('BackupInterval', '7', 'فترة النسخ الاحتياطي بالأيام');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_books_number ON Books(BookNumber);
CREATE INDEX idx_books_date ON Books(BookDate);
CREATE INDEX idx_books_subject ON Books(BookSubject);
CREATE INDEX idx_books_issuing ON Books(IssuingAuthority);
CREATE INDEX idx_books_execution ON Books(ExecutionDepartment);
CREATE INDEX idx_bookimages_bookid ON BookImages(BookID);
CREATE INDEX idx_activitylog_date ON ActivityLog(ActivityDate);
CREATE INDEX idx_activitylog_user ON ActivityLog(UserID);
