{"name": "filename-reserved-regex", "version": "2.0.0", "description": "Regular expression for matching reserved filename characters", "license": "MIT", "repository": "sindresorhus/filename-reserved-regex", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["re", "regex", "regexp", "filename", "reserved", "illegal"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}