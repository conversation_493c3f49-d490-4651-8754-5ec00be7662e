<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج الأرشفة الإلكترونية للكتب</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #69FF97 0%, #00E4FF 50%, #FF00F5 100%); /* Updated: Bright dynamic gradient */
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .welcome-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.98); /* Slightly more opaque */
            padding: 50px;
            border-radius: 25px;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.35); /* Deeper shadow */
            backdrop-filter: blur(10px);
            animation: welcomeEntry 2s ease-out;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent; /* Prepare for gradient border */
            background-clip: padding-box; /* Important for border with background */
            border-image: linear-gradient(135deg, #69FF97, #00E4FF, #FF00F5) 1; /* Gradient border */
        }

        @keyframes welcomeEntry {
            0% { opacity: 0; transform: scale(0.5) rotate(180deg); }
            50% { opacity: 0.7; transform: scale(1.1) rotate(0deg); }
            100% { opacity: 1; transform: scale(1) rotate(0deg); }
        }

        .welcome-container::before { /* Existing glow effect, kept */
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(0, 228, 255, 0.15), transparent); /* Adjusted glow color */
            animation: rotateGlow 4s linear infinite;
        }

        @keyframes rotateGlow {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .app-logo {
            font-size: 72px;
            margin-bottom: 20px;
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .main-title {
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 48px;
            font-weight: bold;
            font-style: italic;
            color: #4A00E0; /* Updated: Vibrant purple */
            margin-bottom: 10px; /* Adjusted margin */
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.25);
            animation: titlePulse 2s ease-in-out infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes titlePulse {
            0%, 100% { 
                transform: scale(1);
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.25);
            }
            50% { 
                transform: scale(1.05);
                text-shadow: 0 0 30px #00E4FF, 3px 3px 6px rgba(0, 0, 0, 0.25);
            }
        }

        .programmer-credit { /* New style for programmer credit */
            font-family: 'Arial', sans-serif;
            font-size: 20px;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 20px;
            background-image: linear-gradient(to right, limegreen, crimson, dodgerblue, mediumorchid, hotpink, orangered);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: textShimmer 2.5s linear infinite;
            background-size: 300% auto; /* Increased size for smoother/wider shimmer */
            position: relative;
            z-index: 1;
        }

        @keyframes textShimmer { /* Shimmer animation for programmer credit */
            0% { background-position: -150% center; } /* Adjusted for 300% size */
            100% { background-position: 150% center; }
        }

        .programmer-info { /* Existing, kept for Arabic info */
            margin: 20px 0; /* Adjusted margin */
            position: relative;
            z-index: 1;
        }

        .programmer-name {
            font-family: 'Arial', sans-serif;
            font-size: 24px;
            font-weight: bold;
            font-style: italic;
            margin-bottom: 10px;
            animation: colorShift 5s ease-in-out infinite; /* Kept, but colors might need adjustment if they clash */
            /* Original colorShift colors: #0066CC, #00AAFF, #8A2BE2, #00AA44, #FF6600 */
            /* Consider updating @keyframes colorShift if needed, or simplify if new credit is primary */
        }
        
        /* Keeping original colorShift, it might still look good or can be fine-tuned later */
        @keyframes colorShift {
            0% { color: #4A00E0; } /* Updated to match new theme */
            20% { color: #00E4FF; }
            40% { color: #FF00F5; }
            60% { color: #69FF97; }
            80% { color: #FF8C00; }
            100% { color: #4A00E0; }
        }

        .department-text {
            font-size: 18px;
            color: #333; /* Darker for better readability on light container */
            font-weight: bold;
            margin-bottom: 30px; /* Adjusted margin */
        }

        .loading-container {
            margin: 30px 0; /* Adjusted margin */
            position: relative;
            z-index: 1;
        }

        .loading-text {
            font-size: 20px;
            color: #4A00E0; /* Updated: Vibrant purple */
            margin-bottom: 20px;
            animation: loadingBlink 1.5s ease-in-out infinite;
        }

        @keyframes loadingBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .progress-bar {
            width: 300px;
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            overflow: hidden;
            margin: 0 auto;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #69FF97, #00E4FF, #FF00F5); /* Updated: Matches body gradient */
            border-radius: 4px;
            animation: progressLoad 3s ease-out forwards;
            position: relative;
        }

        @keyframes progressLoad {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .progress-fill::after { /* Existing shine effect, kept */
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent); /* Slightly more pronounced shine */
            animation: progressShine 1s ease-in-out infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .version-display { /* New style for prominent version display */
            font-family: 'Arial', sans-serif;
            font-size: 22px;
            font-weight: bold;
            color: #1E90FF; /* Bright blue */
            margin-top: 30px;
            margin-bottom: 20px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
            position: relative;
            z-index: 1;
        }

        .button-container { /* New container for buttons */
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 25px;
            gap: 20px; /* Space between buttons */
            position: relative;
            z-index: 1;
        }

        .enter-button {
            background: linear-gradient(45deg, #8E2DE2, #4A00E0); /* Updated: Purple gradient */
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 20px;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(142, 45, 226, 0.45); /* Adjusted shadow */
            transition: all 0.3s ease;
            opacity: 0;
            animation: buttonAppear 1s ease-out 3s forwards;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .exit-button { /* New style for exit button */
            background: linear-gradient(45deg, #FF416C, #FF4B2B); /* Red gradient */
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 20px;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255, 65, 108, 0.45);
            transition: all 0.3s ease;
            opacity: 0;
            animation: buttonAppear 1s ease-out 3s forwards; /* Appear with enter button */
            position: relative;
            z-index: 1;
            overflow: hidden;
        }
        
        @keyframes buttonAppear {
            0% { opacity: 0; transform: translateY(50px) scale(0.5); }
            100% { opacity: 1; transform: translateY(0) scale(1); }
        }

        .enter-button:hover, .exit-button:hover {
            transform: translateY(-3px) scale(1.05);
        }
        .enter-button:hover { box-shadow: 0 12px 35px rgba(142, 45, 226, 0.6); }
        .exit-button:hover { box-shadow: 0 12px 35px rgba(255, 65, 108, 0.6); }


        .enter-button::before, .exit-button::before { /* Ripple effect for both buttons */
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .enter-button:active::before, .exit-button:active::before {
            width: 300px;
            height: 300px;
        }

        .floating-particles { /* Existing, kept */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle { /* Existing, kept */
            position: absolute;
            background: rgba(255, 255, 255, 0.7); /* Slightly more visible particles */
            border-radius: 50%;
            animation: floatUp 6s linear infinite;
        }

        @keyframes floatUp { /* Existing, kept */
            0% { opacity: 0; transform: translateY(100vh) scale(0); }
            10% { opacity: 1; transform: translateY(90vh) scale(1); }
            90% { opacity: 1; transform: translateY(10vh) scale(1); }
            100% { opacity: 0; transform: translateY(0vh) scale(0); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="welcome-container">
        <div class="app-logo">📚</div>
        
        <h1 class="main-title">برنامج الأرشفة الإلكترونية للكتب</h1>
        
        <p class="programmer-credit">Prepared and programmed by the accountant: Ali Ajil Khashan Al-Mahna</p>
        
        <div class="programmer-info">
            <div class="programmer-name">المبرمج: علي عاجل خشان المحنة</div>
            <div class="department-text">(قسم الحسابات – شعبة الرواتب)</div>
        </div>
        
        <div class="loading-container">
            <div class="loading-text">جاري تحميل النظام...</div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <div class="version-display">Program Version 2.0.0</div>
        
        <div class="button-container">
            <button class="enter-button" onclick="enterSystem()">
                دخول النظام
            </button>
            <button class="exit-button" onclick="exitApplication()">
                Exit Application
            </button>
        </div>
    </div>
    
    <script>
        // إنشاء الجسيمات المتحركة (Existing, kept)
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return; // Guard clause
            
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.width = particle.style.height = (Math.random() * 10 + 5) + 'px';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                    if (particlesContainer.children.length < 50) { // Limit total particles
                        particlesContainer.appendChild(particle);
                    }
                    
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 8000); // Increased lifetime slightly for visibility
                }, i * 200); // Faster particle generation
            }
        }

        if (document.getElementById('particles')) { // Only run if particles container exists
             setInterval(createParticles, 3000); // Adjusted interval
             createParticles();
        }

        // وظيفة دخول النظام (Existing, kept)
        function enterSystem() {
            document.body.style.transition = 'all 1s ease-out';
            document.body.style.transform = 'scale(0.8)';
            document.body.style.opacity = '0';
            
            setTimeout(() => {
                window.location.href = 'MainDashboard.html';
            }, 1000);
        }

        // New: وظيفة الخروج من التطبيق
        function exitApplication() {
            // Attempt to close the window/tab.
            // Note: Modern browsers have security restrictions on window.close().
            // It typically only works if the window was opened by a script using window.open().
            window.close(); 
        }

        // تشغيل الصوت عند التحميل (Existing, kept)
        window.addEventListener('load', () => {
            console.log('مرحباً بك في برنامج الأرشفة الإلكترونية للكتب - الإصدار 2.0.0');
        });
    </script>
</body>
</html>
