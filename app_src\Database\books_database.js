// قاعدة بيانات الكتب المحلية
class BooksDatabase {
    constructor() {
        this.dbName = 'BooksArchiveDB';
        this.version = 1;
        this.db = null;
        this.init();
    }

    // تهيئة قاعدة البيانات
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // إنشاء جدول الكتب
                if (!db.objectStoreNames.contains('books')) {
                    const booksStore = db.createObjectStore('books', { 
                        keyPath: 'id', 
                        autoIncrement: true 
                    });
                    
                    // إنشاء فهارس للبحث
                    booksStore.createIndex('bookNumber', 'bookNumber', { unique: true });
                    booksStore.createIndex('bookDate', 'bookDate', { unique: false });
                    booksStore.createIndex('bookSubject', 'bookSubject', { unique: false });
                    booksStore.createIndex('issuingAuthority', 'issuingAuthority', { unique: false });
                    booksStore.createIndex('executionDepartment', 'executionDepartment', { unique: false });
                    booksStore.createIndex('recipientAuthority', 'recipientAuthority', { unique: false });
                    booksStore.createIndex('createdDate', 'createdDate', { unique: false });
                }
                
                // إنشاء جدول الإحصائيات
                if (!db.objectStoreNames.contains('statistics')) {
                    const statsStore = db.createObjectStore('statistics', { 
                        keyPath: 'id' 
                    });
                }
            };
        });
    }

    // حفظ كتاب جديد
    async saveBook(bookData) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['books', 'statistics'], 'readwrite');
            const booksStore = transaction.objectStore('books');
            const statsStore = transaction.objectStore('statistics');
            
            // إضافة معرف فريد وتاريخ الإنشاء
            const book = {
                ...bookData,
                id: Date.now(),
                createdDate: new Date().toISOString(),
                createdBy: 'علي عاجل خشان المحنة'
            };
            
            const request = booksStore.add(book);
            
            request.onsuccess = () => {
                // تحديث الإحصائيات
                this.updateStatistics();
                resolve(book);
            };
            
            request.onerror = () => reject(request.error);
        });
    }

    // جلب جميع الكتب
    async getAllBooks() {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['books'], 'readonly');
            const store = transaction.objectStore('books');
            const request = store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث في الكتب
    async searchBooks(searchTerm) {
        const allBooks = await this.getAllBooks();
        
        if (!searchTerm) return allBooks;
        
        const term = searchTerm.toLowerCase();
        return allBooks.filter(book => 
            book.bookNumber?.toLowerCase().includes(term) ||
            book.bookSubject?.toLowerCase().includes(term) ||
            book.issuingAuthority?.toLowerCase().includes(term) ||
            book.executionDepartment?.toLowerCase().includes(term) ||
            book.recipientAuthority?.toLowerCase().includes(term) ||
            book.fileNumber?.toLowerCase().includes(term) ||
            book.details?.toLowerCase().includes(term)
        );
    }

    // جلب الإحصائيات
    async getStatistics() {
        const books = await this.getAllBooks();
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        const monthlyBooks = books.filter(book => {
            const bookDate = new Date(book.createdDate);
            return bookDate.getMonth() === currentMonth && bookDate.getFullYear() === currentYear;
        });
        
        return {
            totalBooks: books.length,
            monthlyBooks: monthlyBooks.length,
            totalImages: books.reduce((sum, book) => sum + (book.images?.length || 0), 0),
            lastUpdate: books.length > 0 ? new Date(Math.max(...books.map(b => new Date(b.createdDate)))).toLocaleDateString('ar-SA') : 'لا يوجد'
        };
    }

    // تحديث الإحصائيات
    async updateStatistics() {
        const stats = await this.getStatistics();
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['statistics'], 'readwrite');
            const store = transaction.objectStore('statistics');
            
            const request = store.put({
                id: 'main',
                ...stats,
                lastUpdated: new Date().toISOString()
            });
            
            request.onsuccess = () => resolve(stats);
            request.onerror = () => reject(request.error);
        });
    }

    // حذف كتاب
    async deleteBook(id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['books'], 'readwrite');
            const store = transaction.objectStore('books');
            const request = store.delete(id);
            
            request.onsuccess = () => {
                this.updateStatistics();
                resolve();
            };
            request.onerror = () => reject(request.error);
        });
    }

    // تصدير البيانات إلى Excel
    async exportToExcel() {
        const books = await this.getAllBooks();
        const stats = await this.getStatistics();
        
        return {
            books,
            statistics: stats,
            exportDate: new Date().toLocaleDateString('ar-SA'),
            totalCount: books.length
        };
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const booksDB = new BooksDatabase();

// تصدير للاستخدام العام
window.booksDB = booksDB;
