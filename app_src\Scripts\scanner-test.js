// اختبار نظام المسح الضوئي
class ScannerTest {
    constructor() {
        this.testResults = [];
        this.scanner = window.scanner;
        this.pdfGenerator = window.pdfGenerator;
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        console.log('🧪 بدء اختبار نظام المسح الضوئي...');
        
        const tests = [
            this.testScannerAvailability,
            this.testScannerList,
            this.testPDFGenerator,
            this.testElectronAPI,
            this.testFileNaming,
            this.testErrorHandling
        ];

        for (const test of tests) {
            try {
                await test.call(this);
            } catch (error) {
                this.addTestResult(test.name, false, error.message);
            }
        }

        this.displayResults();
        return this.testResults;
    }

    // اختبار توفر الماسح الضوئي
    async testScannerAvailability() {
        console.log('🔍 اختبار توفر الماسح الضوئي...');
        
        if (!this.scanner) {
            throw new Error('كائن الماسح الضوئي غير متوفر');
        }

        const isAvailable = await this.scanner.checkScannerAvailability();
        this.addTestResult('testScannerAvailability', true, 
            `حالة الماسح: ${isAvailable ? 'متوفر' : 'غير متوفر'}`);
    }

    // اختبار قائمة الماسحات
    async testScannerList() {
        console.log('📋 اختبار قائمة الماسحات...');
        
        const scanners = await this.scanner.getScannerList();
        this.addTestResult('testScannerList', true, 
            `عدد الماسحات المكتشفة: ${scanners.length}`);
    }

    // اختبار مولد PDF
    async testPDFGenerator() {
        console.log('📄 اختبار مولد PDF...');
        
        if (!this.pdfGenerator) {
            throw new Error('مولد PDF غير متوفر');
        }

        // اختبار إنشاء PDF وهمي
        const testImageData = {
            data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            format: 'png'
        };

        try {
            const pdf = await this.pdfGenerator.createSinglePagePDF(testImageData, 'test', {
                orientation: 'portrait',
                format: 'a4'
            });
            
            this.addTestResult('testPDFGenerator', true, 'تم إنشاء PDF تجريبي بنجاح');
        } catch (error) {
            this.addTestResult('testPDFGenerator', false, 'فشل في إنشاء PDF: ' + error.message);
        }
    }

    // اختبار Electron API
    async testElectronAPI() {
        console.log('⚡ اختبار Electron API...');
        
        if (!window.electronAPI) {
            this.addTestResult('testElectronAPI', false, 'Electron API غير متوفر');
            return;
        }

        try {
            // اختبار استدعاء وظيفة فحص الماسح
            const result = await window.electronAPI.invoke('check-scanner-status');
            this.addTestResult('testElectronAPI', true, 
                `API يعمل بشكل صحيح. الماسحات المتاحة: ${result.scanners?.length || 0}`);
        } catch (error) {
            this.addTestResult('testElectronAPI', false, 'خطأ في API: ' + error.message);
        }
    }

    // اختبار تسمية الملفات
    async testFileNaming() {
        console.log('📝 اختبار تسمية الملفات...');
        
        const testCases = [
            { subject: 'كتاب تجريبي', expected: 'كتاب تجريبي' },
            { subject: '', expected: 'مستند_ممسوح' },
            { subject: 'كتاب/خاص', expected: 'كتاب_خاص' }
        ];

        let passed = 0;
        for (const testCase of testCases) {
            const result = this.sanitizeFileName(testCase.subject || 'مستند_ممسوح');
            if (result.includes(testCase.expected.replace('/', '_'))) {
                passed++;
            }
        }

        this.addTestResult('testFileNaming', passed === testCases.length, 
            `نجح ${passed} من ${testCases.length} اختبارات التسمية`);
    }

    // اختبار معالجة الأخطاء
    async testErrorHandling() {
        console.log('⚠️ اختبار معالجة الأخطاء...');
        
        try {
            // محاولة مسح بدون ماسح متوفر
            await this.scanner.startScan({ forceError: true });
            this.addTestResult('testErrorHandling', false, 'لم يتم التعامل مع الخطأ بشكل صحيح');
        } catch (error) {
            this.addTestResult('testErrorHandling', true, 'تم التعامل مع الخطأ بشكل صحيح');
        }
    }

    // تنظيف اسم الملف
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .trim();
    }

    // إضافة نتيجة اختبار
    addTestResult(testName, passed, message) {
        this.testResults.push({
            test: testName,
            passed: passed,
            message: message,
            timestamp: new Date().toISOString()
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    // عرض النتائج
    displayResults() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        console.log('\n📊 نتائج الاختبار:');
        console.log(`إجمالي الاختبارات: ${totalTests}`);
        console.log(`نجح: ${passedTests}`);
        console.log(`فشل: ${failedTests}`);
        console.log(`معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);

        // إنشاء تقرير مفصل
        this.createDetailedReport();
    }

    // إنشاء تقرير مفصل
    createDetailedReport() {
        const reportModal = document.createElement('div');
        reportModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        const passedTests = this.testResults.filter(r => r.passed).length;
        const totalTests = this.testResults.length;
        const successRate = Math.round((passedTests / totalTests) * 100);

        const resultsHtml = this.testResults.map(result => `
            <div style="background: ${result.passed ? '#e8f5e8' : '#ffe8e8'}; 
                        padding: 10px; margin: 5px 0; border-radius: 5px; 
                        border-left: 4px solid ${result.passed ? '#4CAF50' : '#f44336'};">
                <strong>${result.passed ? '✅' : '❌'} ${result.test}</strong>
                <br><small>${result.message}</small>
            </div>
        `).join('');

        reportModal.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 15px; 
                        max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h3 style="color: #00CC66; margin-bottom: 20px; text-align: center;">
                    🧪 تقرير اختبار نظام المسح الضوئي
                </h3>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                    <h4 style="margin: 0; color: ${successRate >= 80 ? '#4CAF50' : successRate >= 60 ? '#FF9800' : '#f44336'};">
                        معدل النجاح: ${successRate}%
                    </h4>
                    <p style="margin: 5px 0;">نجح ${passedTests} من ${totalTests} اختبارات</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">تفاصيل الاختبارات:</h4>
                    ${resultsHtml}
                </div>

                <div style="text-align: center;">
                    <button onclick="this.closest('div').remove()" 
                            style="background: #00CC66; color: white; border: none; 
                                   padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        ✅ إغلاق التقرير
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(reportModal);

        // إغلاق عند النقر خارج النافذة
        reportModal.onclick = (e) => {
            if (e.target === reportModal) {
                reportModal.remove();
            }
        };
    }

    // اختبار سريع للماسح الضوئي
    async quickScannerTest() {
        console.log('⚡ اختبار سريع للماسح الضوئي...');
        
        try {
            const isAvailable = await this.scanner.checkScannerAvailability();
            const scanners = await this.scanner.getScannerList();
            
            const result = {
                scannerAvailable: isAvailable,
                scannersCount: scanners.length,
                scanners: scanners,
                electronAPI: !!window.electronAPI,
                pdfGenerator: !!this.pdfGenerator
            };

            console.log('نتائج الاختبار السريع:', result);
            return result;
        } catch (error) {
            console.error('خطأ في الاختبار السريع:', error);
            return { error: error.message };
        }
    }
}

// إنشاء مثيل من اختبار الماسح
const scannerTest = new ScannerTest();

// تصدير للاستخدام العام
window.scannerTest = scannerTest;

// إضافة اختصارات لوحة المفاتيح للاختبار
document.addEventListener('keydown', (event) => {
    // Ctrl + Shift + T لتشغيل الاختبار الكامل
    if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        scannerTest.runAllTests();
    }
    
    // Ctrl + Shift + Q للاختبار السريع
    if (event.ctrlKey && event.shiftKey && event.key === 'Q') {
        event.preventDefault();
        scannerTest.quickScannerTest();
    }
});

console.log('🧪 نظام اختبار المسح الضوئي جاهز!');
console.log('استخدم Ctrl+Shift+T للاختبار الكامل أو Ctrl+Shift+Q للاختبار السريع');
