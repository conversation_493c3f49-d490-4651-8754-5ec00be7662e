' ===================================================================
' إعداد قاعدة البيانات - برنامج الأرشفة الإلكترونية للكتب
' Database Setup Script - Electronic Books Archive System
' المبرمج: علي عاجل خشان المحنة
' ===================================================================

Option Explicit

Dim objAccess, objDB, strDBPath, objFSO

' إنشاء كائنات النظام
Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objAccess = CreateObject("Access.Application")

' تحديد مسار قاعدة البيانات
strDBPath = objFSO.GetAbsolutePathName("BooksArchive.accdb")

' رسالة ترحيب
WScript.Echo "====================================="
WScript.Echo "برنامج الأرشفة الإلكترونية للكتب"
WScript.Echo "إعداد قاعدة البيانات"
WScript.Echo "====================================="
WScript.Echo "المبرمج: علي عاجل خشان المحنة"
WScript.Echo "قسم الحسابات - شعبة الرواتب"
WScript.Echo "====================================="
WScript.Echo ""

' التحقق من وجود قاعدة البيانات
If objFSO.FileExists(strDBPath) Then
    WScript.Echo "تم العثور على قاعدة البيانات الموجودة."
    If MsgBox("هل تريد إعادة إنشاء قاعدة البيانات؟" & vbCrLf & "سيتم حذف جميع البيانات الموجودة!", vbYesNo + vbQuestion, "تأكيد") = vbYes Then
        objFSO.DeleteFile strDBPath
        WScript.Echo "تم حذف قاعدة البيانات القديمة."
    Else
        WScript.Echo "تم إلغاء العملية."
        WScript.Quit
    End If
End If

' إنشاء قاعدة البيانات الجديدة
WScript.Echo "إنشاء قاعدة البيانات الجديدة..."
objAccess.NewCurrentDatabase strDBPath

' الحصول على مرجع قاعدة البيانات
Set objDB = objAccess.CurrentDb

' إنشاء الجداول
WScript.Echo "إنشاء الجداول..."

' جدول الكتب الرئيسي
objDB.Execute "CREATE TABLE Books (" & _
    "BookID AUTOINCREMENT PRIMARY KEY, " & _
    "BookNumber TEXT(50) NOT NULL, " & _
    "BookDate DATETIME NOT NULL, " & _
    "BookSubject TEXT(255), " & _
    "IssuingAuthority TEXT(255), " & _
    "ExecutionDepartment TEXT(255), " & _
    "Details MEMO, " & _
    "RecipientAuthority TEXT(255), " & _
    "FileNumber TEXT(50), " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "ModifiedDate DATETIME DEFAULT Now(), " & _
    "CreatedBy TEXT(100), " & _
    "ModifiedBy TEXT(100)" & _
    ")"

WScript.Echo "تم إنشاء جدول الكتب."

' جدول صور الكتب
objDB.Execute "CREATE TABLE BookImages (" & _
    "ImageID AUTOINCREMENT PRIMARY KEY, " & _
    "BookID LONG, " & _
    "ImageName TEXT(255), " & _
    "ImagePath TEXT(500), " & _
    "ImageDescription TEXT(255), " & _
    "ImageOrder INTEGER DEFAULT 1, " & _
    "CreatedDate DATETIME DEFAULT Now()" & _
    ")"

WScript.Echo "تم إنشاء جدول الصور."

' جدول المستخدمين
objDB.Execute "CREATE TABLE Users (" & _
    "UserID AUTOINCREMENT PRIMARY KEY, " & _
    "Username TEXT(50) UNIQUE NOT NULL, " & _
    "Password TEXT(255) NOT NULL, " & _
    "FullName TEXT(100), " & _
    "Department TEXT(100), " & _
    "UserRole TEXT(20) DEFAULT 'User', " & _
    "IsActive YESNO DEFAULT True, " & _
    "LastLogin DATETIME, " & _
    "CreatedDate DATETIME DEFAULT Now()" & _
    ")"

WScript.Echo "تم إنشاء جدول المستخدمين."

' جدول إعدادات النظام
objDB.Execute "CREATE TABLE SystemSettings (" & _
    "SettingID AUTOINCREMENT PRIMARY KEY, " & _
    "SettingName TEXT(100) UNIQUE NOT NULL, " & _
    "SettingValue TEXT(500), " & _
    "SettingDescription TEXT(255), " & _
    "ModifiedDate DATETIME DEFAULT Now(), " & _
    "ModifiedBy TEXT(100)" & _
    ")"

WScript.Echo "تم إنشاء جدول الإعدادات."

' جدول سجل العمليات
objDB.Execute "CREATE TABLE ActivityLog (" & _
    "LogID AUTOINCREMENT PRIMARY KEY, " & _
    "UserID LONG, " & _
    "ActivityType TEXT(50), " & _
    "ActivityDescription TEXT(500), " & _
    "TableName TEXT(100), " & _
    "RecordID LONG, " & _
    "ActivityDate DATETIME DEFAULT Now()" & _
    ")"

WScript.Echo "تم إنشاء جدول سجل العمليات."

' إنشاء الفهارس
WScript.Echo "إنشاء الفهارس..."

objDB.Execute "CREATE INDEX idx_books_number ON Books(BookNumber)"
objDB.Execute "CREATE INDEX idx_books_date ON Books(BookDate)"
objDB.Execute "CREATE INDEX idx_books_subject ON Books(BookSubject)"
objDB.Execute "CREATE INDEX idx_books_issuing ON Books(IssuingAuthority)"
objDB.Execute "CREATE INDEX idx_books_execution ON Books(ExecutionDepartment)"
objDB.Execute "CREATE INDEX idx_bookimages_bookid ON BookImages(BookID)"
objDB.Execute "CREATE INDEX idx_activitylog_date ON ActivityLog(ActivityDate)"
objDB.Execute "CREATE INDEX idx_activitylog_user ON ActivityLog(UserID)"

WScript.Echo "تم إنشاء الفهارس."

' إدراج البيانات الأساسية
WScript.Echo "إدراج البيانات الأساسية..."

' إدراج مستخدم الأدمن
objDB.Execute "INSERT INTO Users (Username, Password, FullName, Department, UserRole) " & _
    "VALUES ('admin', 'a1234a1234A@#1', 'علي عاجل خشان المحنة', 'قسم الحسابات - شعبة الرواتب', 'Admin')"

WScript.Echo "تم إنشاء حساب الأدمن."

' إدراج الإعدادات الافتراضية
objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('AppTitle', 'برنامج الأرشفة الإلكترونية للكتب', 'عنوان التطبيق')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('AppVersion', '1.0.0', 'إصدار التطبيق')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('DefaultTheme', 'Blue', 'الثيم الافتراضي')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('BackupPath', 'C:\Backup\BooksArchive\', 'مسار النسخ الاحتياطي')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('MaxImageSize', '5242880', 'الحد الأقصى لحجم الصورة بالبايت (5MB)')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('ScannerEnabled', 'True', 'تفعيل الماسح الضوئي')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('AutoBackup', 'True', 'النسخ الاحتياطي التلقائي')"

objDB.Execute "INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES " & _
    "('BackupInterval', '7', 'فترة النسخ الاحتياطي بالأيام')"

WScript.Echo "تم إدراج الإعدادات الافتراضية."

' إدراج بيانات تجريبية
WScript.Echo "إدراج بيانات تجريبية..."

objDB.Execute "INSERT INTO Books (BookNumber, BookDate, BookSubject, IssuingAuthority, ExecutionDepartment, RecipientAuthority, FileNumber, Details, CreatedBy, ModifiedBy) VALUES " & _
    "('2024/001', #2024-01-15#, 'تعليمات جديدة للرواتب', 'وزارة المالية', 'قسم الحسابات', 'شعبة الرواتب', 'ملف-001', 'تعليمات جديدة خاصة بحساب الرواتب والعلاوات', 'علي عاجل خشان المحنة', 'علي عاجل خشان المحنة')"

objDB.Execute "INSERT INTO Books (BookNumber, BookDate, BookSubject, IssuingAuthority, ExecutionDepartment, RecipientAuthority, FileNumber, Details, CreatedBy, ModifiedBy) VALUES " & _
    "('2024/002', #2024-01-20#, 'إجازات الموظفين', 'إدارة الموارد البشرية', 'قسم الشؤون الإدارية', 'جميع الأقسام', 'ملف-002', 'تنظيم إجازات الموظفين للعام الجديد', 'علي عاجل خشان المحنة', 'علي عاجل خشان المحنة')"

objDB.Execute "INSERT INTO Books (BookNumber, BookDate, BookSubject, IssuingAuthority, ExecutionDepartment, RecipientAuthority, FileNumber, Details, CreatedBy, ModifiedBy) VALUES " & _
    "('2024/003', #2024-02-01#, 'تحديث أنظمة الحاسوب', 'قسم تقنية المعلومات', 'قسم الحسابات', 'شعبة الرواتب', 'ملف-003', 'تحديث برامج الحاسوب وأنظمة الأرشفة', 'علي عاجل خشان المحنة', 'علي عاجل خشان المحنة')"

WScript.Echo "تم إدراج البيانات التجريبية."

' تسجيل عملية الإعداد في سجل النشاط
objDB.Execute "INSERT INTO ActivityLog (UserID, ActivityType, ActivityDescription, TableName) VALUES " & _
    "(1, 'Setup', 'إعداد قاعدة البيانات الأولي', 'System')"

' إغلاق قاعدة البيانات
objDB.Close
objAccess.Quit

' تنظيف الكائنات
Set objDB = Nothing
Set objAccess = Nothing
Set objFSO = Nothing

' رسالة النجاح
WScript.Echo ""
WScript.Echo "====================================="
WScript.Echo "تم إعداد قاعدة البيانات بنجاح!"
WScript.Echo "====================================="
WScript.Echo ""
WScript.Echo "تفاصيل الإعداد:"
WScript.Echo "- تم إنشاء 5 جداول رئيسية"
WScript.Echo "- تم إنشاء 8 فهارس لتحسين الأداء"
WScript.Echo "- تم إنشاء حساب الأدمن"
WScript.Echo "- تم إدراج الإعدادات الافتراضية"
WScript.Echo "- تم إدراج 3 كتب تجريبية"
WScript.Echo ""
WScript.Echo "معلومات تسجيل الدخول:"
WScript.Echo "اسم المستخدم: admin"
WScript.Echo "كلمة المرور: a1234a1234A@#1"
WScript.Echo ""
WScript.Echo "يمكنك الآن تشغيل البرنامج!"

MsgBox "تم إعداد قاعدة البيانات بنجاح!" & vbCrLf & vbCrLf & _
       "يمكنك الآن تشغيل برنامج الأرشفة الإلكترونية." & vbCrLf & vbCrLf & _
       "معلومات تسجيل الدخول:" & vbCrLf & _
       "اسم المستخدم: admin" & vbCrLf & _
       "كلمة المرور: a1234a1234A@#1", vbInformation, "إعداد قاعدة البيانات"
