const { app, BrowserWindow, Menu, dialog, ipcMain, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { spawn } = require('child_process');

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
function createRequiredDirectories() {
  const dirs = [
    path.join(__dirname, 'Images', 'BookCovers'),
    path.join(__dirname, 'Images', 'Icons'),
    path.join(__dirname, 'Assets'),
    path.join(__dirname, 'Database'),
    path.join(__dirname, 'DataSave')
  ];

  // إنشاء مجلد افتراضي لحفظ الصور في مجلد المستندات
  const documentsPath = path.join(os.homedir(), 'Documents', 'BookArchiveScans');
  dirs.push(documentsPath);

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  // حفظ مسار المجلد الافتراضي في الإعدادات إذا لم يكن موجودًا
  const settingsPath = path.join(__dirname, 'DataSave', 'settings.json');
  let settings = {};
  
  if (fs.existsSync(settingsPath)) {
    try {
      settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
    } catch (error) {
      console.error('Error reading settings file:', error);
    }
  }

  if (!settings.scanSavePath) {
    settings.scanSavePath = documentsPath;
    try {
      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
    } catch (error) {
      console.error('Error writing settings file:', error);
    }
  }
}

// وظائف المسح الضوئي المحسنة
async function scanDocument(options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // إذا كان الطلب للتحقق فقط
      if (options.checkOnly) {
        checkScannerAvailability()
          .then(result => resolve(result))
          .catch(error => reject(error));
        return;
      }

      // التحقق من وجود الماسح الضوئي أولاً
      checkScannerAvailability()
        .then(scannerStatus => {
          if (!scannerStatus.hasAvailableScanner) {
            reject(new Error('لم يتم العثور على ماسح ضوئي متاح'));
            return;
          }

          // بدء عملية المسح الفعلية
          startActualScan(options)
            .then(result => resolve(result))
            .catch(error => reject(error));
        })
        .catch(error => reject(error));

    } catch (error) {
      reject(new Error('خطأ في الوصول للماسح الضوئي: ' + error.message));
    }
  });
}

// التحقق من توفر الماسح الضوئي
async function checkScannerAvailability() {
  return new Promise((resolve, reject) => {
    const scannerProcess = spawn('powershell', [
      '-Command',
      `
      $scanners = Get-WmiObject -Class Win32_PnPEntity | Where-Object {
        $_.Name -like "*scan*" -or
        $_.Name -like "*imaging*" -or
        $_.Name -like "*Canon*" -or
        $_.Name -like "*HP*" -or
        $_.Name -like "*Epson*" -or
        $_.Name -like "*Brother*"
      } | Where-Object { $_.Status -eq "OK" }

      if ($scanners) {
        Write-Output "SCANNER_FOUND"
        $scanners | ForEach-Object { Write-Output $_.Name }
      } else {
        Write-Output "NO_SCANNER"
      }
      `
    ]);

    let output = '';
    scannerProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    scannerProcess.on('close', (code) => {
      const hasScanner = output.includes('SCANNER_FOUND');
      resolve({
        success: true,
        hasAvailableScanner: hasScanner,
        scanners: hasScanner ? output.split('\n').slice(1).filter(line => line.trim()) : []
      });
    });

    scannerProcess.on('error', (error) => {
      reject(new Error('خطأ في التحقق من الماسح الضوئي: ' + error.message));
    });
  });
}

// بدء المسح الفعلي
async function startActualScan(options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // استخدام Windows Image Acquisition (WIA) للمسح
      const scanScript = `
        $deviceManager = New-Object -ComObject WIA.DeviceManager
        $device = $deviceManager.DeviceInfos.Item(1).Connect()
        $item = $device.Items.Item(1)

        # إعداد خصائص المسح
        $item.Properties.Item("6146").Value = ${options.resolution || 300}  # DPI
        $item.Properties.Item("6147").Value = ${options.resolution || 300}  # DPI

        # بدء المسح
        $image = $item.Transfer()

        # حفظ الصورة مؤقتاً
        $tempPath = "$env:TEMP\\scanned_image_" + (Get-Date -Format "yyyyMMdd_HHmmss") + ".png"
        $image.SaveFile($tempPath)

        Write-Output $tempPath
      `;

      const scanCommand = spawn('powershell', ['-Command', scanScript]);

      let scannedImagePath = '';
      scanCommand.stdout.on('data', (data) => {
        scannedImagePath += data.toString().trim();
      });

      scanCommand.on('close', (code) => {
        if (code === 0 && scannedImagePath) {
          // قراءة الصورة وتحويلها إلى base64
          try {
            const imageBuffer = fs.readFileSync(scannedImagePath);
            const base64Image = 'data:image/png;base64,' + imageBuffer.toString('base64');

            // حذف الملف المؤقت
            fs.unlinkSync(scannedImagePath);

            resolve({
              success: true,
              data: {
                imageData: base64Image,
                format: 'png',
                resolution: options.resolution || 300,
                timestamp: new Date().toISOString()
              }
            });
          } catch (fileError) {
            reject(new Error('خطأ في قراءة الصورة الممسوحة: ' + fileError.message));
          }
        } else {
          reject(new Error('فشل في عملية المسح'));
        }
      });

      scanCommand.on('error', (error) => {
        reject(new Error('خطأ في تشغيل الماسح الضوئي: ' + error.message));
      });

    } catch (error) {
      reject(new Error('خطأ في بدء المسح: ' + error.message));
    }
  });
}

// حفظ الصورة الممسوحة كـ PDF
async function saveScannedImageAsPDF(imagePath, outputPath, fileName) {
  return new Promise((resolve, reject) => {
    try {
      // استخدام PowerShell لتحويل الصورة إلى PDF
      const convertCommand = spawn('powershell', [
        '-Command',
        `
        Add-Type -AssemblyName System.Drawing
        $image = [System.Drawing.Image]::FromFile('${imagePath}')
        $pdf = New-Object System.Drawing.Printing.PrintDocument
        $pdf.PrinterSettings.PrinterName = 'Microsoft Print to PDF'
        $pdf.PrinterSettings.PrintToFile = $true
        $pdf.PrinterSettings.PrintFileName = '${outputPath}'
        # هنا يمكن إضافة المزيد من منطق التحويل
        `
      ]);

      convertCommand.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, path: outputPath });
        } else {
          reject(new Error('فشل في تحويل الصورة إلى PDF'));
        }
      });

    } catch (error) {
      reject(new Error('خطأ في حفظ PDF: ' + error.message));
    }
  });
}

function createWindow() {
  // إنشاء نافذة المتصفح
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: 'برنامج الأرشفة الإلكترونية للكتب - علي عاجل خشان المحنة',
    icon: path.join(__dirname, 'Assets', 'app_icon.ico'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  });

  // تحميل صفحة لوحة المعلومات الرئيسية
  mainWindow.loadFile(path.join(__dirname, 'Forms', 'MainDashboard.html'));

  // إنشاء قائمة مخصصة
  const template = [
    {
      label: 'الصفحة الرئيسية',
      click: () => mainWindow.loadFile(path.join(__dirname, 'Forms', 'MainDashboard.html'))
    },
    {
      label: 'إدخال بيانات',
      click: () => mainWindow.loadFile(path.join(__dirname, 'Forms', 'BookEntry.html'))
    },
    {
      label: 'بحث وتقرير',
      click: () => mainWindow.loadFile(path.join(__dirname, 'Forms', 'Reports.html'))
    },
    {
      label: 'لوحة الأدمن',
      click: () => mainWindow.loadFile(path.join(__dirname, 'Forms', 'AdminPanel.html'))
    },
    {
      label: 'الإعدادات',
      click: () => mainWindow.loadFile(path.join(__dirname, 'Forms', 'Settings.html'))
    },
    {
      label: 'خروج',
      click: () => app.quit()
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // فتح أدوات المطور في وضع التطوير
  // mainWindow.webContents.openDevTools();
}

// استقبال طلبات اختيار مجلد من الواجهة
ipcMain.on('select-directory', (event) => {
  dialog.showOpenDialog({
    properties: ['openDirectory']
  }).then(result => {
    if (!result.canceled) {
      event.reply('directory-selected', result.filePaths[0]);
    }
  }).catch(err => {
    console.error(err);
  });
});

// استقبال طلبات حفظ الإعدادات
ipcMain.on('save-settings', (event, settings) => {
  const settingsPath = path.join(__dirname, 'DataSave', 'settings.json');
  try {
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
    event.reply('settings-saved', true);
  } catch (error) {
    console.error('Error saving settings:', error);
    event.reply('settings-saved', false);
  }
});

// استقبال طلبات قراءة الإعدادات
ipcMain.on('load-settings', (event) => {
  const settingsPath = path.join(__dirname, 'DataSave', 'settings.json');
  if (fs.existsSync(settingsPath)) {
    try {
      const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      event.reply('settings-loaded', settings);
    } catch (error) {
      console.error('Error reading settings:', error);
      event.reply('settings-loaded', {});
    }
  } else {
    event.reply('settings-loaded', {});
  }
});

// معالج طلبات المسح الضوئي
ipcMain.handle('scan-document', async (event, options) => {
  try {
    const result = await scanDocument(options);
    return { success: true, data: result };
  } catch (error) {
    console.error('Scanner error:', error);
    return { success: false, error: error.message };
  }
});

// معالج حفظ الصورة الممسوحة كـ PDF
ipcMain.handle('save-scanned-pdf', async (event, imagePath, outputDir, fileName) => {
  try {
    const outputPath = path.join(outputDir, fileName + '.pdf');
    const result = await saveScannedImageAsPDF(imagePath, outputPath, fileName);
    return { success: true, path: outputPath };
  } catch (error) {
    console.error('PDF save error:', error);
    return { success: false, error: error.message };
  }
});

// معالج فتح مجلد الحفظ
ipcMain.handle('open-save-folder', async (event, folderPath) => {
  try {
    await shell.openPath(folderPath);
    return { success: true };
  } catch (error) {
    console.error('Open folder error:', error);
    return { success: false, error: error.message };
  }
});

// معالج دمج عدة صفحات في PDF واحد
ipcMain.handle('save-combined-pdf', async (event, pagesData, outputDir, fileName) => {
  try {
    const outputPath = path.join(outputDir, fileName + '.pdf');

    // هنا يمكن استخدام مكتبة PDF لدمج الصفحات
    // مؤقتاً سنحفظ كل صفحة منفصلة
    const savedPages = [];

    for (let i = 0; i < pagesData.pages.length; i++) {
      const pageFileName = `${fileName}_page_${i + 1}`;
      const pageResult = await saveScannedImageAsPDF(
        pagesData.pages[i].data,
        path.join(outputDir, pageFileName + '.pdf'),
        pageFileName
      );
      savedPages.push(pageResult.path);
    }

    return {
      success: true,
      path: outputPath,
      pages: savedPages,
      message: `تم حفظ ${pagesData.pages.length} صفحة بنجاح`
    };
  } catch (error) {
    console.error('Combined PDF save error:', error);
    return { success: false, error: error.message };
  }
});

// معالج التحقق من حالة الماسح الضوئي
ipcMain.handle('check-scanner-status', async (event) => {
  try {
    return new Promise((resolve) => {
      const checkProcess = spawn('powershell', [
        '-Command',
        `
        $scanners = Get-WmiObject -Class Win32_PnPEntity | Where-Object {
          $_.Name -like "*scan*" -or
          $_.Name -like "*imaging*" -or
          $_.DeviceID -like "*USB\\VID_*" -and $_.Name -like "*scan*"
        }
        if ($scanners) {
          $scanners | ForEach-Object {
            Write-Output "$($_.Name)|$($_.Status)|$($_.DeviceID)"
          }
        } else {
          Write-Output "NO_SCANNER_FOUND"
        }
        `
      ]);

      let output = '';
      checkProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      checkProcess.on('close', (code) => {
        const scanners = [];
        const lines = output.trim().split('\n');

        lines.forEach(line => {
          if (line && line !== 'NO_SCANNER_FOUND') {
            const [name, status, deviceId] = line.split('|');
            scanners.push({
              name: name?.trim(),
              status: status?.trim(),
              deviceId: deviceId?.trim(),
              available: status?.trim() === 'OK'
            });
          }
        });

        resolve({
          success: true,
          scanners: scanners,
          hasAvailableScanner: scanners.some(s => s.available)
        });
      });

      checkProcess.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          scanners: [],
          hasAvailableScanner: false
        });
      });
    });
  } catch (error) {
    return {
      success: false,
      error: error.message,
      scanners: [],
      hasAvailableScanner: false
    };
  }
});

// سيتم استدعاء هذه الطريقة عندما ينتهي Electron من التهيئة
app.whenReady().then(() => {
  createRequiredDirectories();
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// الخروج عندما تكون جميع النوافذ مغلقة، باستثناء على macOS
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});
