{"name": "at-least-node", "version": "1.0.0", "description": "Lightweight Node.js version sniffing/comparison", "keywords": ["semver", "feature"], "homepage": "https://github.com/RyanZim/at-least-node#readme", "bugs": {"url": "https://github.com/RyanZim/at-least-node/issues"}, "repository": {"type": "git", "url": "git+https://github.com/RyanZim/at-least-node.git"}, "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "index.js", "files": [], "scripts": {"test": "ava"}, "devDependencies": {"ava": "^3.1.0", "rewire": "^4.0.1", "semver": "^7.1.2"}, "engines": {"node": ">= 4.0.0"}}