{"devDependencies": {"electron": "^36.3.2", "electron-packager": "^17.1.2"}, "name": "alqaisar-1", "version": "1.0.0", "description": "برنامج الأرشفة الإلكترونية للكتب - علي عاجل خشان المحنة", "main": "main.js", "dependencies": {"at-least-node": "^1.0.0", "author-regex": "^1.0.0", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "bluebird": "^3.7.2", "boolean": "^3.2.0", "brace-expansion": "^1.1.11", "buffer-crc32": "^0.2.13", "buffer-equal": "^1.0.1", "cacheable-lookup": "^5.0.4", "cacheable-request": "^7.0.4", "clone-response": "^1.0.3", "commander": "^5.1.0", "compare-version": "^0.1.2", "concat-map": "^0.0.1", "cross-spawn": "^7.0.6", "cross-spawn-windows-exe": "^1.2.0", "debug": "^4.4.1", "decompress-response": "^6.0.0", "defer-to-connect": "^2.0.1", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "detect-node": "^2.1.0", "dir-compare": "^3.3.0", "end-of-stream": "^1.4.4", "env-paths": "^2.2.1", "error-ex": "^1.3.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es6-error": "^4.1.1", "escape-string-regexp": "^4.0.0", "extract-zip": "^2.0.1", "fd-slicer": "^1.1.0", "filename-reserved-regex": "^2.0.0", "filenamify": "^4.3.0", "find-up": "^2.1.0", "flora-colossus": "^2.0.0", "fs-extra": "^11.3.0", "fs.realpath": "^1.0.0", "function-bind": "^1.1.2", "galactus": "^1.0.0", "get-package-info": "^1.0.0", "get-stream": "^5.2.0", "glob": "^7.2.3", "global-agent": "^3.0.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "got": "^11.8.6", "graceful-fs": "^4.2.11", "has-property-descriptors": "^1.0.2", "hasown": "^2.0.2", "hosted-git-info": "^2.8.9", "http-cache-semantics": "^4.2.0", "http2-wrapper": "^1.0.3", "inflight": "^1.0.6", "inherits": "^2.0.4", "is-arrayish": "^0.2.1", "is-core-module": "^2.16.1", "is-docker": "^2.2.1", "is-wsl": "^2.2.0", "isbinaryfile": "^4.0.10", "isexe": "^2.0.0", "json-buffer": "^3.0.1", "json-stringify-safe": "^5.0.1", "jsonfile": "^6.1.0", "junk": "^3.1.0", "keyv": "^4.5.4", "load-json-file": "^2.0.0", "locate-path": "^2.0.0", "lodash.get": "^4.4.2", "lowercase-keys": "^2.0.0", "matcher": "^3.0.0", "mimic-response": "^1.0.1", "minimatch": "^3.1.2", "minimist": "^1.2.8", "ms": "^2.1.3", "normalize-package-data": "^2.5.0", "normalize-url": "^6.1.0", "object-keys": "^1.1.1", "once": "^1.4.0", "p-cancelable": "^2.1.1", "p-limit": "^1.3.0", "p-locate": "^2.0.0", "p-try": "^1.0.0", "parse-author": "^2.0.0", "parse-json": "^2.2.0", "path-exists": "^3.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "path-parse": "^1.0.7", "path-type": "^2.0.0", "pend": "^1.2.0", "pify": "^2.3.0", "plist": "^3.1.0", "progress": "^2.0.3", "pump": "^3.0.2", "quick-lru": "^5.1.1", "rcedit": "^3.1.0", "read-pkg": "^2.0.0", "read-pkg-up": "^2.0.0", "resolve": "^1.22.10", "resolve-alpn": "^1.2.1", "responselike": "^2.0.1", "roarr": "^2.15.4", "semver": "^7.7.2", "semver-compare": "^1.0.0", "serialize-error": "^7.0.1", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "spdx-correct": "^3.2.0", "spdx-exceptions": "^2.5.0", "spdx-expression-parse": "^3.0.1", "spdx-license-ids": "^3.0.21", "sprintf-js": "^1.1.3", "strip-bom": "^3.0.0", "strip-outer": "^1.0.1", "sumchecker": "^3.0.1", "supports-preserve-symlinks-flag": "^1.0.0", "trim-repeated": "^1.0.0", "type-fest": "^0.13.1", "undici-types": "^6.21.0", "universalify": "^2.0.1", "validate-npm-package-license": "^3.0.4", "which": "^2.0.2", "wrappy": "^1.0.2", "xmlbuilder": "^15.1.1", "yargs-parser": "^21.1.1", "yauzl": "^2.10.0", "jspdf": "^2.5.1", "canvas": "^2.11.2"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "electron .", "build": "electron-packager . \"برنامج الأرشفة الإلكترونية\" --platform=win32 --arch=x64 --out=dist/ --overwrite --asar", "build-desktop": "electron-packager . \"برنامج الأرشفة الإلكترونية\" --platform=win32 --arch=x64 --out=%USERPROFILE%/Desktop/ --overwrite --asar"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}