<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - برنامج الأرشفة الإلكترونية</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 50%, #CCE7FF 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #666666, #888888);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .settings-container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .settings-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #666666;
        }

        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #666666;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 24px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-weight: bold;
            color: #333;
            flex: 1;
        }

        .setting-description {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .setting-control {
            min-width: 200px;
            text-align: left;
        }

        .theme-selector {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .theme-option {
            width: 40px;
            height: 40px;
            border: 3px solid transparent;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .theme-option:hover {
            transform: scale(1.1);
            border-color: #333;
        }

        .theme-option.active {
            border-color: #666666;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .theme-blue { background: linear-gradient(45deg, #0066CC, #00AAFF); }
        .theme-green { background: linear-gradient(45deg, #00AA44, #00CC55); }
        .theme-purple { background: linear-gradient(45deg, #8A2BE2, #9932CC); }
        .theme-sky { background: linear-gradient(45deg, #00BFFF, #1E90FF); }
        .theme-dark { background: linear-gradient(45deg, #333333, #555555); }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #666666;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .input-field {
            padding: 10px;
            border: 2px solid #666666;
            border-radius: 8px;
            font-size: 14px;
            width: 100%;
            box-sizing: border-box;
        }

        .select-field {
            padding: 10px;
            border: 2px solid #666666;
            border-radius: 8px;
            font-size: 14px;
            width: 100%;
            background: white;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .action-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .save-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 204, 102, 0.4);
        }

        .backup-btn {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
        }

        .backup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        .repair-btn {
            background: linear-gradient(45deg, #FF9900, #FFAA33);
            color: white;
        }

        .repair-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 153, 0, 0.4);
        }

        .reset-btn {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 51, 51, 0.4);
        }

        .navigation {
            text-align: center;
            margin: 30px 0;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #00CC66, #00FF80);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .setting-control {
                width: 100%;
                min-width: auto;
            }
            
            .theme-selector {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1>إعدادات النظام</h1>
        <p>تخصيص وإدارة إعدادات برنامج الأرشفة الإلكترونية</p>
    </header>

    <div class="navigation">
        <button class="nav-button" onclick="goBack()">🔙 العودة للوحة المعلومات</button>
    </div>

    <div class="settings-container">
        <!-- إعدادات المظهر -->
        <div class="settings-section">
            <h2 class="section-title">
                <span class="section-icon">🎨</span>
                إعدادات المظهر
            </h2>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">ثيم الألوان</div>
                    <div class="setting-description">اختر ثيم الألوان المفضل للواجهة</div>
                </div>
                <div class="setting-control">
                    <div class="theme-selector">
                        <div class="theme-option theme-blue active" onclick="selectTheme('blue')" title="الثيم الأزرق الناصع البراق"></div>
                        <div class="theme-option theme-green" onclick="selectTheme('green')" title="الثيم الأخضر الناصع البراق"></div>
                        <div class="theme-option theme-purple" onclick="selectTheme('purple')" title="الثيم البنفسجي الناصع البراق"></div>
                        <div class="theme-option theme-sky" onclick="selectTheme('sky')" title="الثيم السماوي الناصع البراق"></div>
                        <div class="theme-option theme-dark" onclick="selectTheme('dark')" title="الثيم الداكن"></div>
                    </div>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">الوضع الليلي</div>
                    <div class="setting-description">تفعيل الوضع الليلي للعيون</div>
                </div>
                <div class="setting-control">
                    <label class="switch">
                        <input type="checkbox" id="darkMode">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">حجم الخط</div>
                    <div class="setting-description">تحديد حجم الخط في الواجهة</div>
                </div>
                <div class="setting-control">
                    <select class="select-field" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="settings-section">
            <h2 class="section-title">
                <span class="section-icon">⚙️</span>
                إعدادات النظام
            </h2>

            <div class="setting-item">
                <div>
                    <div class="setting-label">تفعيل الماسح الضوئي</div>
                    <div class="setting-description">تفعيل دعم الماسح الضوئي لمسح الوثائق</div>
                </div>
                <div class="setting-control">
                    <label class="switch">
                        <input type="checkbox" id="scannerEnabled" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">النسخ الاحتياطي التلقائي</div>
                    <div class="setting-description">إنشاء نسخة احتياطية تلقائياً</div>
                </div>
                <div class="setting-control">
                    <label class="switch">
                        <input type="checkbox" id="autoBackup" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">فترة النسخ الاحتياطي</div>
                    <div class="setting-description">كم يوم بين كل نسخة احتياطية</div>
                </div>
                <div class="setting-control">
                    <select class="select-field" id="backupInterval">
                        <option value="1">يومياً</option>
                        <option value="3">كل 3 أيام</option>
                        <option value="7" selected>أسبوعياً</option>
                        <option value="30">شهرياً</option>
                    </select>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">مسار النسخ الاحتياطي</div>
                    <div class="setting-description">المجلد المخصص لحفظ النسخ الاحتياطية</div>
                </div>
                <div class="setting-control">
                    <input type="text" class="input-field" id="backupPath" value="C:\Backup\BooksArchive\" readonly>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">الحد الأقصى لحجم الصورة</div>
                    <div class="setting-description">الحد الأقصى لحجم الصورة بالميجابايت</div>
                </div>
                <div class="setting-control">
                    <select class="select-field" id="maxImageSize">
                        <option value="2">2 ميجابايت</option>
                        <option value="5" selected>5 ميجابايت</option>
                        <option value="10">10 ميجابايت</option>
                        <option value="20">20 ميجابايت</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- إعدادات الأمان -->
        <div class="settings-section">
            <h2 class="section-title">
                <span class="section-icon">🔒</span>
                إعدادات الأمان
            </h2>

            <div class="setting-item">
                <div>
                    <div class="setting-label">تسجيل العمليات</div>
                    <div class="setting-description">تسجيل جميع العمليات في سجل النشاط</div>
                </div>
                <div class="setting-control">
                    <label class="switch">
                        <input type="checkbox" id="activityLogging" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">مهلة انتهاء الجلسة</div>
                    <div class="setting-description">المدة بالدقائق قبل انتهاء الجلسة تلقائياً</div>
                </div>
                <div class="setting-control">
                    <select class="select-field" id="sessionTimeout">
                        <option value="30">30 دقيقة</option>
                        <option value="60" selected>60 دقيقة</option>
                        <option value="120">120 دقيقة</option>
                        <option value="0">بدون انتهاء</option>
                    </select>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">تشفير البيانات</div>
                    <div class="setting-description">تشفير البيانات الحساسة في قاعدة البيانات</div>
                </div>
                <div class="setting-control">
                    <label class="switch">
                        <input type="checkbox" id="dataEncryption">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- أدوات الصيانة -->
        <div class="settings-section">
            <h2 class="section-title">
                <span class="section-icon">🔧</span>
                أدوات الصيانة
            </h2>

            <div class="setting-item">
                <div>
                    <div class="setting-label">إنشاء نسخة احتياطية</div>
                    <div class="setting-description">إنشاء نسخة احتياطية فورية من قاعدة البيانات</div>
                </div>
                <div class="setting-control">
                    <button class="action-btn backup-btn" onclick="createBackup()">
                        💾 إنشاء نسخة احتياطية
                    </button>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">ضغط وإصلاح قاعدة البيانات</div>
                    <div class="setting-description">ضغط قاعدة البيانات وإصلاح أي مشاكل</div>
                </div>
                <div class="setting-control">
                    <button class="action-btn repair-btn" onclick="compactAndRepair()">
                        🔧 ضغط وإصلاح
                    </button>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">مسح الملفات المؤقتة</div>
                    <div class="setting-description">حذف الملفات المؤقتة وتنظيف النظام</div>
                </div>
                <div class="setting-control">
                    <button class="action-btn repair-btn" onclick="cleanTempFiles()">
                        🧹 تنظيف النظام
                    </button>
                </div>
            </div>

            <div class="setting-item">
                <div>
                    <div class="setting-label">إعادة تعيين الإعدادات</div>
                    <div class="setting-description">إعادة جميع الإعدادات إلى القيم الافتراضية</div>
                </div>
                <div class="setting-control">
                    <button class="action-btn reset-btn" onclick="resetSettings()">
                        🔄 إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="action-buttons">
            <button class="action-btn save-btn" onclick="saveSettings()">
                💾 حفظ الإعدادات
            </button>
        </div>
    </div>

    <script>
        let currentTheme = 'blue';

        // تحميل الإعدادات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadSettings();
        });

        // تحميل الإعدادات المحفوظة
        function loadSettings() {
            // تحميل الثيم المحفوظ
            const savedTheme = localStorage.getItem('selectedTheme') || 'blue';
            selectTheme(savedTheme);

            // تحميل باقي الإعدادات من localStorage
            const darkMode = localStorage.getItem('darkMode') === 'true';
            document.getElementById('darkMode').checked = darkMode;

            const fontSize = localStorage.getItem('fontSize') || 'medium';
            document.getElementById('fontSize').value = fontSize;

            const scannerEnabled = localStorage.getItem('scannerEnabled') !== 'false';
            document.getElementById('scannerEnabled').checked = scannerEnabled;

            const autoBackup = localStorage.getItem('autoBackup') !== 'false';
            document.getElementById('autoBackup').checked = autoBackup;

            const backupInterval = localStorage.getItem('backupInterval') || '7';
            document.getElementById('backupInterval').value = backupInterval;

            const maxImageSize = localStorage.getItem('maxImageSize') || '5';
            document.getElementById('maxImageSize').value = maxImageSize;

            const activityLogging = localStorage.getItem('activityLogging') !== 'false';
            document.getElementById('activityLogging').checked = activityLogging;

            const sessionTimeout = localStorage.getItem('sessionTimeout') || '60';
            document.getElementById('sessionTimeout').value = sessionTimeout;

            const dataEncryption = localStorage.getItem('dataEncryption') === 'true';
            document.getElementById('dataEncryption').checked = dataEncryption;
        }

        // اختيار الثيم
        function selectTheme(theme) {
            // إزالة التحديد من جميع الثيمات
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
            });

            // تحديد الثيم الجديد
            document.querySelector(`.theme-${theme}`).classList.add('active');
            currentTheme = theme;

            // تطبيق الثيم على الصفحة
            document.body.className = `theme-${theme}`;
        }

        // حفظ الإعدادات
        function saveSettings() {
            // حفظ الثيم
            localStorage.setItem('selectedTheme', currentTheme);

            // حفظ باقي الإعدادات
            localStorage.setItem('darkMode', document.getElementById('darkMode').checked);
            localStorage.setItem('fontSize', document.getElementById('fontSize').value);
            localStorage.setItem('scannerEnabled', document.getElementById('scannerEnabled').checked);
            localStorage.setItem('autoBackup', document.getElementById('autoBackup').checked);
            localStorage.setItem('backupInterval', document.getElementById('backupInterval').value);
            localStorage.setItem('maxImageSize', document.getElementById('maxImageSize').value);
            localStorage.setItem('activityLogging', document.getElementById('activityLogging').checked);
            localStorage.setItem('sessionTimeout', document.getElementById('sessionTimeout').value);
            localStorage.setItem('dataEncryption', document.getElementById('dataEncryption').checked);

            // إظهار رسالة النجاح
            alert('تم حفظ الإعدادات بنجاح! ✅\n\nسيتم تطبيق الإعدادات الجديدة عند إعادة تشغيل البرنامج.');
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
                showProgress();
                
                // محاكاة عملية النسخ الاحتياطي
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    updateProgress(progress);
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        hideProgress();
                        alert('تم إنشاء النسخة الاحتياطية بنجاح! ✅\n\nالمسار: C:\\Backup\\BooksArchive\\BooksArchive_Backup_' + new Date().toISOString().slice(0,10) + '.accdb');
                    }
                }, 200);
            }
        }

        // ضغط وإصلاح قاعدة البيانات
        function compactAndRepair() {
            if (confirm('هل تريد ضغط وإصلاح قاعدة البيانات؟\n\nسيتم إغلاق البرنامج مؤقتاً أثناء العملية.')) {
                showProgress();
                
                // محاكاة عملية الضغط والإصلاح
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    updateProgress(progress);
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        hideProgress();
                        alert('تم ضغط وإصلاح قاعدة البيانات بنجاح! ✅\n\nتم تحسين الأداء وإصلاح أي مشاكل موجودة.');
                    }
                }, 300);
            }
        }

        // تنظيف الملفات المؤقتة
        function cleanTempFiles() {
            if (confirm('هل تريد حذف الملفات المؤقتة وتنظيف النظام؟')) {
                showProgress();
                
                // محاكاة عملية التنظيف
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 20;
                    updateProgress(progress);
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        hideProgress();
                        alert('تم تنظيف النظام بنجاح! ✅\n\nتم حذف 15.7 ميجابايت من الملفات المؤقتة.');
                    }
                }, 150);
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟\n\nسيتم فقدان جميع التخصيصات الحالية.')) {
                // مسح جميع الإعدادات المحفوظة
                localStorage.removeItem('selectedTheme');
                localStorage.removeItem('darkMode');
                localStorage.removeItem('fontSize');
                localStorage.removeItem('scannerEnabled');
                localStorage.removeItem('autoBackup');
                localStorage.removeItem('backupInterval');
                localStorage.removeItem('maxImageSize');
                localStorage.removeItem('activityLogging');
                localStorage.removeItem('sessionTimeout');
                localStorage.removeItem('dataEncryption');

                // إعادة تحميل الإعدادات الافتراضية
                loadSettings();

                alert('تم إعادة تعيين جميع الإعدادات بنجاح! ✅');
            }
        }

        // إظهار شريط التقدم
        function showProgress() {
            document.getElementById('progressBar').style.display = 'block';
            updateProgress(0);
        }

        // تحديث شريط التقدم
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // إخفاء شريط التقدم
        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
            }, 500);
        }

        // العودة للوحة المعلومات
        function goBack() {
            window.location.href = 'MainDashboard.html';
        }
    </script>
</body>
</html>
