<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء صور تجريبية - برنامج الأرشفة الإلكترونية</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
            margin: 0;
            padding: 20px;
            text-align: center;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            color: #0066CC;
            margin-bottom: 30px;
        }

        .sample-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .image-card {
            border: 2px solid #0066CC;
            border-radius: 10px;
            padding: 15px;
            background: #f8f9fa;
        }

        .sample-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .image-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .image-description {
            font-size: 12px;
            color: #666;
        }

        .download-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,204,102,0.3);
        }

        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .note {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إنشاء صور تجريبية للأرشيف</h1>
        
        <div class="instructions">
            <h3>تعليمات الاستخدام:</h3>
            <p>هذه الصفحة تساعدك في إنشاء صور تجريبية لاختبار برنامج الأرشفة الإلكترونية.</p>
            <p>انقر على زر "تحميل" أسفل كل صورة لحفظها في مجلد BookCovers.</p>
        </div>

        <div class="sample-images">
            <div class="image-card">
                <canvas class="sample-image" id="canvas1" width="200" height="150"></canvas>
                <div class="image-name">تعليمات_الرواتب.jpg</div>
                <div class="image-description">صورة تجريبية لكتاب تعليمات الرواتب</div>
                <button class="download-btn" onclick="downloadImage('canvas1', 'تعليمات_الرواتب.jpg')">تحميل</button>
            </div>

            <div class="image-card">
                <canvas class="sample-image" id="canvas2" width="200" height="150"></canvas>
                <div class="image-name">إجازات_الموظفين.jpg</div>
                <div class="image-description">صورة تجريبية لكتاب إجازات الموظفين</div>
                <button class="download-btn" onclick="downloadImage('canvas2', 'إجازات_الموظفين.jpg')">تحميل</button>
            </div>

            <div class="image-card">
                <canvas class="sample-image" id="canvas3" width="200" height="150"></canvas>
                <div class="image-name">تحديث_الأنظمة.jpg</div>
                <div class="image-description">صورة تجريبية لكتاب تحديث الأنظمة</div>
                <button class="download-btn" onclick="downloadImage('canvas3', 'تحديث_الأنظمة.jpg')">تحميل</button>
            </div>

            <div class="image-card">
                <canvas class="sample-image" id="canvas4" width="200" height="150"></canvas>
                <div class="image-name">كتاب_رسمي.jpg</div>
                <div class="image-description">صورة تجريبية عامة لكتاب رسمي</div>
                <button class="download-btn" onclick="downloadImage('canvas4', 'كتاب_رسمي.jpg')">تحميل</button>
            </div>

            <div class="image-card">
                <canvas class="sample-image" id="canvas5" width="200" height="150"></canvas>
                <div class="image-name">وثيقة_إدارية.jpg</div>
                <div class="image-description">صورة تجريبية لوثيقة إدارية</div>
                <button class="download-btn" onclick="downloadImage('canvas5', 'وثيقة_إدارية.jpg')">تحميل</button>
            </div>

            <div class="image-card">
                <canvas class="sample-image" id="canvas6" width="200" height="150"></canvas>
                <div class="image-name">تعميم_عام.jpg</div>
                <div class="image-description">صورة تجريبية لتعميم عام</div>
                <button class="download-btn" onclick="downloadImage('canvas6', 'تعميم_عام.jpg')">تحميل</button>
            </div>
        </div>

        <div class="note">
            <strong>ملاحظة:</strong> هذه صور تجريبية للاختبار فقط. في الاستخدام الفعلي، ستقوم بإرفاق صور حقيقية للكتب والوثائق.
        </div>

        <button class="download-btn" onclick="downloadAllImages()" style="font-size: 16px; padding: 15px 30px;">
            تحميل جميع الصور
        </button>
    </div>

    <script>
        // إنشاء الصور التجريبية
        function createSampleImage(canvasId, title, subtitle, color1, color2) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // إنشاء تدرج لوني
            const gradient = ctx.createLinearGradient(0, 0, 200, 150);
            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);
            
            // رسم الخلفية
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 200, 150);
            
            // إضافة حدود
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 3;
            ctx.strokeRect(5, 5, 190, 140);
            
            // إضافة النص
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 2;
            ctx.shadowOffsetX = 1;
            ctx.shadowOffsetY = 1;
            
            // العنوان الرئيسي
            ctx.fillText(title, 100, 60);
            
            // العنوان الفرعي
            ctx.font = '12px Arial';
            ctx.fillText(subtitle, 100, 80);
            
            // إضافة رقم الكتاب
            ctx.font = 'bold 14px Arial';
            ctx.fillText('2024/' + (canvasId.slice(-1)).padStart(3, '0'), 100, 110);
            
            // إضافة التاريخ
            ctx.font = '10px Arial';
            ctx.fillText(new Date().toLocaleDateString('ar-SA'), 100, 130);
        }

        // إنشاء جميع الصور
        createSampleImage('canvas1', 'تعليمات الرواتب', 'وزارة المالية', '#0066CC', '#00AAFF');
        createSampleImage('canvas2', 'إجازات الموظفين', 'الموارد البشرية', '#00AA44', '#00CC55');
        createSampleImage('canvas3', 'تحديث الأنظمة', 'تقنية المعلومات', '#8A2BE2', '#9932CC');
        createSampleImage('canvas4', 'كتاب رسمي', 'الإدارة العامة', '#FF9900', '#FFAA33');
        createSampleImage('canvas5', 'وثيقة إدارية', 'الشؤون الإدارية', '#FF3333', '#FF6666');
        createSampleImage('canvas6', 'تعميم عام', 'جميع الأقسام', '#00BFFF', '#1E90FF');

        // تحميل صورة واحدة
        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }

        // تحميل جميع الصور
        function downloadAllImages() {
            const images = [
                {id: 'canvas1', name: 'تعليمات_الرواتب.jpg'},
                {id: 'canvas2', name: 'إجازات_الموظفين.jpg'},
                {id: 'canvas3', name: 'تحديث_الأنظمة.jpg'},
                {id: 'canvas4', name: 'كتاب_رسمي.jpg'},
                {id: 'canvas5', name: 'وثيقة_إدارية.jpg'},
                {id: 'canvas6', name: 'تعميم_عام.jpg'}
            ];

            images.forEach((img, index) => {
                setTimeout(() => {
                    downloadImage(img.id, img.name);
                }, index * 500); // تأخير 500ms بين كل تحميل
            });

            alert('سيتم تحميل جميع الصور تلقائياً.\nاحفظها في مجلد BookCovers لاستخدامها في البرنامج.');
        }

        // رسالة ترحيب
        window.addEventListener('load', () => {
            setTimeout(() => {
                alert('مرحباً بك في أداة إنشاء الصور التجريبية!\n\nهذه الأداة تساعدك في إنشاء صور تجريبية لاختبار برنامج الأرشفة الإلكترونية.\n\nانقر على "تحميل" أسفل كل صورة لحفظها، أو استخدم "تحميل جميع الصور" لحفظ الكل مرة واحدة.');
            }, 1000);
        });
    </script>
</body>
</html>
