<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث وتقرير - برنامج الأرشفة الإلكترونية</title>
    <link rel="stylesheet" href="../Styles/themes.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 50%, #CCE7FF 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #8A2BE2, #9932CC);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .reports-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .report-card {
            background: white;
            border: 2px solid #8A2BE2;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(138, 43, 226, 0.3);
            border-color: #9932CC;
        }

        .report-icon {
            font-size: 48px;
            color: #8A2BE2;
            margin-bottom: 15px;
        }

        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #8A2BE2;
            margin-bottom: 10px;
        }

        .report-description {
            color: #666;
            font-size: 14px;
        }

        .statistics-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #8A2BE2;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #8A2BE2;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
            border: 2px dashed #8A2BE2;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .report-table th {
            background: linear-gradient(45deg, #8A2BE2, #9932CC);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .report-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: right;
        }

        .report-table tr:hover {
            background-color: #F8F0FF;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .filter-input {
            padding: 10px;
            border: 2px solid #8A2BE2;
            border-radius: 8px;
            font-size: 14px;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .filter-btn {
            background: linear-gradient(45deg, #8A2BE2, #9932CC);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
        }

        .export-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }

        .export-btn {
            background: linear-gradient(45deg, #00CC66, #00FF80);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 204, 102, 0.4);
        }

        .print-btn {
            background: linear-gradient(45deg, #FF9900, #FFAA33);
        }

        .print-btn:hover {
            box-shadow: 0 6px 20px rgba(255, 153, 0, 0.4);
        }

        .navigation {
            text-align: center;
            margin: 30px 0;
        }

        .nav-button {
            background: linear-gradient(45deg, #0066CC, #00AAFF);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 102, 204, 0.4);
        }

        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .export-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        /* أنماط البحث الجديد */
        .search-section {
            background: white;
            margin: 20px;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #FF3333;
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .search-group {
            display: flex;
            flex-direction: column;
        }

        .search-group.full-width {
            grid-column: 1 / -1;
        }

        .search-group label {
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .search-input {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f9f9f9;
        }

        .search-input:focus {
            border-color: #FF3333;
            background: white;
            outline: none;
            box-shadow: 0 0 10px rgba(255,51,51,0.2);
        }

        .search-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .search-btn, .export-btn {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .search-btn:hover, .export-btn:hover {
            background: linear-gradient(45deg, #CC0000, #FF3333);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,51,51,0.3);
        }

        /* جدول النتائج */
        .results-section {
            background: white;
            margin: 20px;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #00CC66;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .results-table th {
            background: linear-gradient(45deg, #FF3333, #FF6666);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #000;
        }

        .results-table td {
            padding: 12px 10px;
            text-align: right;
            border: 1px solid #000;
            background: #f9f9f9;
        }

        .results-table tr:nth-child(even) td {
            background: #fff;
        }

        .results-table tr:hover td {
            background: #fff5f5;
        }

        .action-btn {
            padding: 6px 12px;
            margin: 2px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }

        .view-btn {
            background: #0066CC;
            color: white;
        }

        .edit-btn {
            background: #FF9900;
            color: white;
        }

        .delete-btn {
            background: #FF3333;
            color: white;
        }
    </style>
</head>
<body>
    <header class="header">
        <h1>بحث وتقرير</h1>
        <p>بحث متقدم في الكتب وإنشاء تقارير مفصلة</p>
    </header>

    <div class="navigation">
        <button class="nav-button" onclick="goBack()">🔙 العودة للوحة المعلومات</button>
    </div>

    <!-- قسم البحث الجديد -->
    <div class="search-section">
        <h2>🔍 بحث متقدم في الكتب</h2>
        <div class="search-grid">
            <div class="search-group">
                <label for="searchBookNumber">البحث حسب رقم الكتاب:</label>
                <input type="text" id="searchBookNumber" class="search-input" placeholder="أدخل رقم الكتاب...">
            </div>
            <div class="search-group">
                <label for="searchExecutionDept">البحث حسب جهة التنفيذ:</label>
                <input type="text" id="searchExecutionDept" class="search-input" placeholder="أدخل جهة التنفيذ...">
            </div>
            <div class="search-group">
                <label for="searchBank">البحث حسب المصرف:</label>
                <input type="text" id="searchBank" class="search-input" placeholder="أدخل اسم المصرف...">
            </div>
            <div class="search-group">
                <label for="searchRecipient">البحث حسب الجهة المرسل إليها:</label>
                <input type="text" id="searchRecipient" class="search-input" placeholder="أدخل الجهة المرسل إليها...">
            </div>
            <div class="search-group">
                <label for="searchSubject">البحث حسب العنوان:</label>
                <input type="text" id="searchSubject" class="search-input" placeholder="أدخل عنوان الكتاب...">
            </div>
            <div class="search-group full-width">
                <label for="generalSearch">بحث عام:</label>
                <input type="text" id="generalSearch" class="search-input" placeholder="بحث في جميع الحقول..." style="width: 50%;">
            </div>
        </div>
        <div class="search-actions">
            <button class="search-btn" onclick="performSearch()">🔍 بحث</button>
            <button class="export-btn" onclick="exportSearchResults()">📊 تصدير النتائج</button>
            <button class="clear-btn" onclick="clearSearch()">🗑️ مسح الحقول</button>
        </div>
    </div>

    <!-- جدول النتائج -->
    <div class="results-section" id="resultsSection" style="display: none;">
        <h2>📋 نتائج البحث</h2>
        <div class="table-container">
            <table class="results-table" id="resultsTable">
                <thead>
                    <tr>
                        <th>رقم الكتاب</th>
                        <th>تاريخ الكتاب</th>
                        <th>موضوع الكتاب</th>
                        <th>الجهة الصادر منها</th>
                        <th>جهة التنفيذ</th>
                        <th>الجهة المرسل إليها</th>
                        <th>رقم الإضبارة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <div class="reports-container">
        <!-- بطاقات التقارير -->
        <div class="reports-grid">
            <div class="report-card" onclick="generateAllBooksReport()">
                <div class="report-icon">📋</div>
                <h3 class="report-title">تقرير جميع الكتب</h3>
                <p class="report-description">تقرير شامل بجميع الكتب المسجلة في النظام</p>
            </div>

            <div class="report-card" onclick="generateMonthlyReport()">
                <div class="report-icon">📅</div>
                <h3 class="report-title">التقرير الشهري</h3>
                <p class="report-description">تقرير الكتب المضافة خلال الشهر الحالي</p>
            </div>

            <div class="report-card" onclick="generateByDepartmentReport()">
                <div class="report-icon">🏢</div>
                <h3 class="report-title">تقرير حسب الجهة</h3>
                <p class="report-description">تقرير مصنف حسب الجهات المختلفة</p>
            </div>

            <div class="report-card" onclick="exportToExcel()">
                <div class="report-icon">📊</div>
                <h3 class="report-title">تصدير إلى Excel</h3>
                <p class="report-description">تصدير جميع البيانات إلى ملف Excel احترافي</p>
            </div>
        </div>

        <!-- قسم الإحصائيات السريعة -->
        <div class="statistics-section">
            <h2 class="section-title">الإحصائيات السريعة</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalBooks">0</div>
                    <div class="stat-label">إجمالي الكتب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="monthlyBooks">0</div>
                    <div class="stat-label">كتب هذا الشهر</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalImages">0</div>
                    <div class="stat-label">إجمالي الصور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalDepartments">0</div>
                    <div class="stat-label">عدد الجهات</div>
                </div>
            </div>
        </div>



        <!-- جدول التقرير -->
        <div id="reportSection" style="display: none;">
            <div class="export-buttons">
                <button class="export-btn" onclick="exportToExcel()">📊 تصدير إلى Excel</button>
                <button class="export-btn print-btn" onclick="printReport()">🖨️ طباعة التقرير</button>
            </div>

            <table class="report-table" id="reportTable">
                <thead>
                    <tr>
                        <th>رقم الإضبارة</th>
                        <th>الجهة المرسل إليها</th>
                        <th>جهة التنفيذ</th>
                        <th>الجهة الصادر منها</th>
                        <th>موضوع الكتاب</th>
                        <th>تاريخ الكتاب</th>
                        <th>رقم الكتاب</th>
                    </tr>
                </thead>
                <tbody id="reportTableBody">
                </tbody>
            </table>
        </div>

        <!-- أزرار المعاينة والتصدير -->
        <div class="chart-container">
            <h2 class="section-title">أدوات التقارير المتقدمة</h2>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; margin: 20px 0;">
                <button class="export-btn" onclick="previewReport()">👁️ معاينة التقرير</button>
                <button class="export-btn" onclick="exportToExcelAdvanced()">📊 تصدير Excel متقدم</button>
                <button class="export-btn print-btn" onclick="printReportAdvanced()">🖨️ طباعة متقدمة</button>
                <button class="export-btn" onclick="exportToPDF()">📄 تصدير PDF</button>
            </div>
        </div>
    </div>

    <script src="../Database/books_database.js"></script>
    <script src="../Scripts/excel_export.js"></script>
    <script>
        // بيانات تجريبية
        const sampleData = [
            {
                bookNumber: "2024/001",
                bookDate: "2024-01-15",
                bookSubject: "تعليمات جديدة للرواتب",
                issuingAuthority: "وزارة المالية",
                executionDepartment: "قسم الحسابات",
                recipientAuthority: "شعبة الرواتب",
                fileNumber: "ملف-001"
            },
            {
                bookNumber: "2024/002",
                bookDate: "2024-01-20",
                bookSubject: "إجازات الموظفين",
                issuingAuthority: "إدارة الموارد البشرية",
                executionDepartment: "قسم الشؤون الإدارية",
                recipientAuthority: "جميع الأقسام",
                fileNumber: "ملف-002"
            },
            {
                bookNumber: "2024/003",
                bookDate: "2024-02-01",
                bookSubject: "تحديث أنظمة الحاسوب",
                issuingAuthority: "قسم تقنية المعلومات",
                executionDepartment: "قسم الحسابات",
                recipientAuthority: "شعبة الرواتب",
                fileNumber: "ملف-003"
            }
        ];

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            loadStatistics();
            setDefaultDates();
        });

        // تحميل الإحصائيات
        function loadStatistics() {
            // هنا يتم جلب الإحصائيات من قاعدة البيانات
            // مؤقتاً نستخدم بيانات ثابتة
            document.getElementById('totalBooks').textContent = '0';
            document.getElementById('monthlyBooks').textContent = '0';
            document.getElementById('totalImages').textContent = '0';
            document.getElementById('totalDepartments').textContent = '0';
        }

        // تعيين التواريخ الافتراضية
        function setDefaultDates() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('fromDate').value = firstDay.toISOString().split('T')[0];
            document.getElementById('toDate').value = today.toISOString().split('T')[0];
        }

        // تقرير جميع الكتب
        function generateAllBooksReport() {
            showReport('تقرير جميع الكتب', sampleData);
        }

        // التقرير الشهري
        function generateMonthlyReport() {
            const currentMonth = new Date().getMonth();
            const monthlyData = sampleData.filter(book => {
                const bookMonth = new Date(book.bookDate).getMonth();
                return bookMonth === currentMonth;
            });
            showReport('التقرير الشهري', monthlyData);
        }

        // تقرير حسب الجهة
        function generateByDepartmentReport() {
            showReport('تقرير حسب الجهة', sampleData);
        }

        // معاينة التقرير
        function previewReport() {
            const reportData = getCurrentReportData();
            showReportPreview(reportData);
        }

        // تصدير Excel متقدم
        function exportToExcelAdvanced() {
            const reportData = getCurrentReportData();
            createExcelFile(reportData);
        }

        // طباعة متقدمة
        function printReportAdvanced() {
            const reportData = getCurrentReportData();
            showPrintPreview(reportData);
        }

        // تصدير PDF
        function exportToPDF() {
            const reportData = getCurrentReportData();
            createPDFFile(reportData);
        }

        // عرض التقرير
        function showReport(title, data) {
            const reportSection = document.getElementById('reportSection');
            const tableBody = document.getElementById('reportTableBody');
            
            // مسح البيانات السابقة
            tableBody.innerHTML = '';
            
            // إضافة البيانات الجديدة
            data.forEach(book => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${book.fileNumber || '-'}</td>
                    <td>${book.recipientAuthority || '-'}</td>
                    <td>${book.executionDepartment || '-'}</td>
                    <td>${book.issuingAuthority || '-'}</td>
                    <td>${book.bookSubject || '-'}</td>
                    <td>${book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : ''}</td>
                    <td>${book.bookNumber}</td>
                `;
                tableBody.appendChild(row);
            });
            
            // إظهار قسم التقرير
            reportSection.style.display = 'block';
            
            // التمرير إلى التقرير
            reportSection.scrollIntoView({ behavior: 'smooth' });
            
            alert(`تم إنشاء ${title} بنجاح!\nعدد السجلات: ${data.length}`);
        }

        // وظائف البحث الجديدة
        async function performSearch() {
            try {
                // جلب جميع الكتب من قاعدة البيانات
                const allBooks = await booksDB.getAllBooks();

                // الحصول على قيم البحث
                const searchBookNumber = document.getElementById('searchBookNumber').value.trim().toLowerCase();
                const searchExecutionDept = document.getElementById('searchExecutionDept').value.trim().toLowerCase();
                const searchBank = document.getElementById('searchBank').value.trim().toLowerCase();
                const searchRecipient = document.getElementById('searchRecipient').value.trim().toLowerCase();
                const searchSubject = document.getElementById('searchSubject').value.trim().toLowerCase();
                const generalSearch = document.getElementById('generalSearch').value.trim().toLowerCase();

                // تطبيق البحث
                let filteredBooks = allBooks.filter(book => {
                    // البحث العام في جميع الحقول
                    if (generalSearch) {
                        const searchText = generalSearch;
                        return (
                            book.bookNumber?.toLowerCase().includes(searchText) ||
                            book.bookSubject?.toLowerCase().includes(searchText) ||
                            book.issuingAuthority?.toLowerCase().includes(searchText) ||
                            book.executionDepartment?.toLowerCase().includes(searchText) ||
                            book.recipientAuthority?.toLowerCase().includes(searchText) ||
                            book.fileNumber?.toLowerCase().includes(searchText) ||
                            book.details?.toLowerCase().includes(searchText)
                        );
                    }

                    // البحث المخصص
                    let matches = true;

                    if (searchBookNumber && !book.bookNumber?.toLowerCase().includes(searchBookNumber)) {
                        matches = false;
                    }
                    if (searchExecutionDept && !book.executionDepartment?.toLowerCase().includes(searchExecutionDept)) {
                        matches = false;
                    }
                    if (searchBank && !book.issuingAuthority?.toLowerCase().includes(searchBank)) {
                        matches = false;
                    }
                    if (searchRecipient && !book.recipientAuthority?.toLowerCase().includes(searchRecipient)) {
                        matches = false;
                    }
                    if (searchSubject && !book.bookSubject?.toLowerCase().includes(searchSubject)) {
                        matches = false;
                    }

                    return matches;
                });

                // عرض النتائج
                displaySearchResults(filteredBooks);

            } catch (error) {
                console.error('خطأ في البحث:', error);
                alert('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');
            }
        }

        // عرض نتائج البحث في الجدول
        function displaySearchResults(books) {
            const resultsSection = document.getElementById('resultsSection');
            const tableBody = document.getElementById('resultsTableBody');

            // مسح النتائج السابقة
            tableBody.innerHTML = '';

            if (books.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">لا توجد نتائج مطابقة لمعايير البحث</td></tr>';
            } else {
                books.forEach(book => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${book.bookNumber || 'غير محدد'}</td>
                        <td>${book.bookDate || 'غير محدد'}</td>
                        <td>${book.bookSubject || 'غير محدد'}</td>
                        <td>${book.issuingAuthority || 'غير محدد'}</td>
                        <td>${book.executionDepartment || 'غير محدد'}</td>
                        <td>${book.recipientAuthority || 'غير محدد'}</td>
                        <td>${book.fileNumber || 'غير محدد'}</td>
                        <td>
                            <button class="action-btn view-btn" onclick="viewBook(${book.id})">عرض</button>
                            <button class="action-btn edit-btn" onclick="editBook(${book.id})">تعديل</button>
                            <button class="action-btn delete-btn" onclick="deleteBook(${book.id})">حذف</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            // إظهار قسم النتائج
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // مسح حقول البحث
        function clearSearch() {
            document.getElementById('searchBookNumber').value = '';
            document.getElementById('searchExecutionDept').value = '';
            document.getElementById('searchBank').value = '';
            document.getElementById('searchRecipient').value = '';
            document.getElementById('searchSubject').value = '';
            document.getElementById('generalSearch').value = '';

            // إخفاء النتائج
            document.getElementById('resultsSection').style.display = 'none';
        }

        // تصدير نتائج البحث
        async function exportSearchResults() {
            const tableBody = document.getElementById('resultsTableBody');
            const rows = tableBody.querySelectorAll('tr');

            if (rows.length === 0 || (rows.length === 1 && rows[0].cells[0].colSpan === 8)) {
                alert('لا توجد نتائج للتصدير. يرجى إجراء بحث أولاً.');
                return;
            }

            try {
                // إنشاء مصدر Excel
                const exporter = new ExcelExporter();
                exporter.createWorkbook();

                // تحضير البيانات
                const headers = ['رقم الكتاب', 'تاريخ الكتاب', 'موضوع الكتاب', 'الجهة الصادر منها', 'جهة التنفيذ', 'الجهة المرسل إليها', 'رقم الإضبارة'];
                const data = [];

                rows.forEach(row => {
                    if (row.cells[0].colSpan !== 8) { // تجاهل رسالة "لا توجد نتائج"
                        const rowData = [];
                        for (let i = 0; i < 7; i++) { // تجاهل عمود الإجراءات
                            rowData.push(row.cells[i].textContent.trim());
                        }
                        data.push(rowData);
                    }
                });

                // إضافة الورقة
                exporter.addWorksheet('نتائج البحث', data, headers);

                // تصدير الملف
                const fileName = `نتائج_البحث_${new Date().toISOString().split('T')[0]}.xlsx`;
                exporter.exportWorkbook(fileName);

                alert('تم تصدير النتائج بنجاح!');

            } catch (error) {
                console.error('خطأ في التصدير:', error);
                alert('حدث خطأ أثناء التصدير. يرجى المحاولة مرة أخرى.');
            }
        }

        // عرض تفاصيل الكتاب
        function viewBook(id) {
            alert(`عرض تفاصيل الكتاب رقم: ${id}`);
            // هنا يمكن إضافة نافذة منبثقة لعرض التفاصيل
        }

        // تعديل الكتاب
        function editBook(id) {
            if (confirm(`هل تريد تعديل الكتاب رقم: ${id}؟`)) {
                // هنا يمكن فتح نموذج التعديل
                alert('سيتم إضافة وظيفة التعديل قريباً');
            }
        }

        // حذف الكتاب
        async function deleteBook(id) {
            if (confirm('هل أنت متأكد من حذف هذا الكتاب؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                try {
                    await booksDB.deleteBook(id);
                    alert('تم حذف الكتاب بنجاح!');

                    // إعادة تحميل النتائج
                    performSearch();

                } catch (error) {
                    console.error('خطأ في حذف الكتاب:', error);
                    alert('حدث خطأ أثناء حذف الكتاب.');
                }
            }
        }

        // الحصول على بيانات التقرير الحالي
        function getCurrentReportData() {
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const issuingAuthority = document.getElementById('issuingAuthority').value;
            const executionDepartment = document.getElementById('executionDepartment').value;

            let filteredData = sampleData.filter(book => {
                let matches = true;
                if (fromDate && book.bookDate < fromDate) matches = false;
                if (toDate && book.bookDate > toDate) matches = false;
                if (issuingAuthority && book.issuingAuthority !== issuingAuthority) matches = false;
                if (executionDepartment && book.executionDepartment !== executionDepartment) matches = false;
                return matches;
            });

            return {
                title: 'تقرير الأرشيف الإلكتروني',
                subtitle: 'برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة',
                department: 'قسم الحسابات - شعبة الرواتب',
                date: new Date().toLocaleDateString('ar-SA'),
                filters: { fromDate, toDate, issuingAuthority, executionDepartment },
                data: filteredData,
                totalCount: filteredData.length
            };
        }

        // معاينة التقرير
        function showReportPreview(reportData) {
            const previewWindow = window.open('', '_blank', 'width=1000,height=700');
            const previewHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>معاينة التقرير</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                        .report-container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                        .header { text-align: center; border-bottom: 3px solid #0066CC; padding-bottom: 20px; margin-bottom: 30px; }
                        .title { font-size: 24px; font-weight: bold; color: #0066CC; margin-bottom: 10px; }
                        .subtitle { font-size: 16px; color: #666; margin-bottom: 5px; }
                        .department { font-size: 14px; color: #888; }
                        .filters { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; }
                        .filters h4 { margin: 0 0 10px 0; color: #0066CC; }
                        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .table th { background: #0066CC; color: white; padding: 12px; text-align: right; border: 1px solid #ddd; }
                        .table td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                        .table tr:nth-child(even) { background: #f8f9fa; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #eee; color: #666; }
                        .print-btn { background: #0066CC; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
                        .export-btn { background: #00CC66; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="title">${reportData.title}</div>
                            <div class="subtitle">${reportData.subtitle}</div>
                            <div class="department">${reportData.department}</div>
                            <div style="margin-top: 10px; font-size: 14px;">تاريخ التقرير: ${reportData.date}</div>
                        </div>

                        <div class="filters">
                            <h4>مرشحات التقرير:</h4>
                            <p>من تاريخ: ${reportData.filters.fromDate || 'غير محدد'} | إلى تاريخ: ${reportData.filters.toDate || 'غير محدد'}</p>
                            <p>الجهة الصادر منها: ${reportData.filters.issuingAuthority || 'جميع الجهات'} | جهة التنفيذ: ${reportData.filters.executionDepartment || 'جميع الجهات'}</p>
                            <p><strong>إجمالي السجلات: ${reportData.totalCount}</strong></p>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>رقم الإضبارة</th>
                                    <th>الجهة المرسل إليها</th>
                                    <th>جهة التنفيذ</th>
                                    <th>الجهة الصادر منها</th>
                                    <th>موضوع الكتاب</th>
                                    <th>تاريخ الكتاب</th>
                                    <th>رقم الكتاب</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${reportData.data.map(book => `
                                    <tr>
                                        <td>${book.fileNumber || '-'}</td>
                                        <td>${book.recipientAuthority || '-'}</td>
                                        <td>${book.executionDepartment || '-'}</td>
                                        <td>${book.issuingAuthority || '-'}</td>
                                        <td>${book.bookSubject || '-'}</td>
                                        <td>${book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : ''}</td>
                                        <td>${book.bookNumber}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <div class="footer">
                            <p>© 2024 برنامج الأرشفة الإلكترونية للكتب - المبرمج: علي عاجل خشان المحنة</p>
                            <button class="print-btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="export-btn" onclick="window.opener.createExcelFile(${JSON.stringify(reportData).replace(/"/g, '&quot;')})">📊 تصدير Excel</button>
                        </div>
                    </div>
                </body>
                </html>
            `;
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();
        }

        // إنشاء ملف Excel
        function createExcelFile(reportData) {
            // إنشاء محتوى CSV للتصدير
            let csvContent = '\uFEFF'; // BOM for UTF-8

            // العنوان الرئيسي
            csvContent += `"${reportData.title}"\n`;
            csvContent += `"${reportData.subtitle}"\n`;
            csvContent += `"${reportData.department}"\n`;
            csvContent += `"تاريخ التقرير: ${reportData.date}"\n\n`;

            // المرشحات
            csvContent += '"مرشحات التقرير:"\n';
            csvContent += `"من تاريخ: ${reportData.filters.fromDate || 'غير محدد'}"\n`;
            csvContent += `"إلى تاريخ: ${reportData.filters.toDate || 'غير محدد'}"\n`;
            csvContent += `"الجهة الصادر منها: ${reportData.filters.issuingAuthority || 'جميع الجهات'}"\n`;
            csvContent += `"جهة التنفيذ: ${reportData.filters.executionDepartment || 'جميع الجهات'}"\n`;
            csvContent += `"إجمالي السجلات: ${reportData.totalCount}"\n\n`;

            // رؤوس الأعمدة
            csvContent += '"رقم الكتاب","تاريخ الكتاب","موضوع الكتاب","الجهة الصادر منها","جهة التنفيذ","الجهة المرسل إليها","رقم الإضبارة"\n';

            // البيانات
            reportData.data.forEach(book => {
                const formattedDate = book.bookDate ? new Date(book.bookDate).toLocaleDateString('ar-SA') : '';
                csvContent += `"${book.bookNumber}","${formattedDate}","${book.bookSubject || ''}","${book.issuingAuthority || ''}","${book.executionDepartment || ''}","${book.recipientAuthority || ''}","${book.fileNumber || ''}"\n`;
            });

            // إنشاء الملف وتحميله
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير_الأرشيف_${new Date().toISOString().slice(0,10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('تم تصدير التقرير إلى ملف Excel بنجاح! 📊\n\nيمكنك فتح الملف في Microsoft Excel لعرض البيانات في جدول احترافي.');
        }

        // إنشاء ملف PDF
        function createPDFFile(reportData) {
            alert('سيتم تصدير التقرير إلى ملف PDF...\n\nملاحظة: يمكنك استخدام معاينة التقرير ثم طباعة كـ PDF من المتصفح.');
            previewReport();
        }

        // طباعة متقدمة
        function showPrintPreview(reportData) {
            showReportPreview(reportData);
        }

        // تصدير إلى Excel (الوظيفة القديمة محدثة)
        function exportToExcel() {
            const reportData = getCurrentReportData();
            createExcelFile(reportData);
        }

        // طباعة التقرير (الوظيفة القديمة محدثة)
        function printReport() {
            const reportData = getCurrentReportData();
            showReportPreview(reportData);
        }

        // تم حذف وظيفة formatDate لتجنب التداخل البرمجي

        // العودة للوحة المعلومات
        function goBack() {
            window.location.href = 'MainDashboard.html';
        }
    </script>
</body>
</html>
